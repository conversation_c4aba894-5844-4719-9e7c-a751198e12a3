#!/usr/bin/python
# -*- coding:utf-8 -*-
# @Time : 2024/5/17 11:15
# <AUTHOR> Dong Gan
import copy
import os
import sys
import random

sys.path.append(sys.path[0] + "/../")
import json
import time
import numpy as np
import pandas as pd
from itertools import product
import xgboost as xgb
import lightgbm as lgb
from FeaturesLib.math import read_json
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import warnings
# from bayes_opt import BayesianOptimization
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import mean_squared_error
import gc
from ShareUtil.generator_and_test_func import feature_validation_for_given_feature_df
#
"""
使用线性模型或者非线性模型进行多因子训练

x: x-mean-lag-list

rolling_method: rolling-lag  0 / 120 / 250 #滚动训练窗口长度

y: y-lag-list  y-type

pred_y: y-mean-lag
"""


def df_read_parquet(data_path):#按日期读入数据
    if os.path.exists(data_path):
        while True:
            try:
                df_ = pd.read_parquet(data_path)
                return df_
            except Exception as e:
                time.sleep(1.0)
    else:
        return pd.DataFrame()


def test_given_feature(feature_name, feature_df, wrk_path, group_num, holding_period, is_normalization, y_type, save_path, feature_importance, ror_benchmark="", pool_benchmark=""):
    start_ts = time.time()
    final_res = feature_validation_for_given_feature_df(
        feature_df=feature_df,
        feature_name=feature_name,
        start_date=feature_df.columns[0],
        end_date=feature_df.columns[-1],
        group_num=group_num,
        holding_period=holding_period,
        is_normalization=is_normalization,
        is_neutralization=0,
        ror_benchmark=ror_benchmark,
        pool_benchmark=pool_benchmark,
        style_features=[],
        y_type=y_type,
        save_path=save_path,
        work_path=wrk_path,
    )
    max_ic_ = np.max(np.fabs(final_res["rank_ic_mean_series"]))
    rank_corr_ = abs(final_res["bkt_rank_corr"])
    max_ror_ = final_res["bkt_annual_ror_series"].max()
    max_extra_ror_ = final_res["bkt_extra_annual_ror_series"].max()
    print("coming in valid", feature_name, "\n")
    #
    tr_df = final_res["turnover_df"]#可以从计算费后收益的方式反推换手率的计算方式
    bkt_extra_ror_df = final_res["bkt_extra_ror_df"]
    ror_after_fee_df = bkt_extra_ror_df - tr_df * 0.0006 #双边手续费为千分之零点六
    top_df = ror_after_fee_df[["G_10"]]
    top_df["year"] = [i[:4] for i in top_df.index]
    ror_info = {}
    md_info = {}
    for year_, year_df in top_df.groupby(by="year", sort=True):
        ror_series_ = year_df["G_10"].values.astype(np.float64)
        ror_series_ = ror_series_[~np.isnan(ror_series_)]
        # 计算每年的年化收益率和最大回撤
        ror_info[year_] = round(np.nanmean(ror_series_) * 252, 3)
        md_info[year_] = round(np.max(np.maximum.accumulate(np.cumsum(ror_series_)) - np.cumsum(ror_series_)), 3)
        #使用numpy包含的maximum.accumulate函数计算累计收益率序列在每个时点上的最大值，用这个序列减去当前的累计收益序列获得回撤序列，再使用np.max()函数获得最大回撤序列
    # 计算整个区间的年化收益率和最大回撤
    ror_series_ = top_df["G_10"].values.astype(np.float64)
    ror_info["all"] = round(np.mean(ror_series_) * 252, 3)
    md_info["all"] = round(np.max(np.maximum.accumulate(np.cumsum(ror_series_)) - np.cumsum(ror_series_)), 3)

    if not os.path.exists(rf"{save_path}/feature_validation/{feature_name}/performance"):
        os.makedirs(rf"{save_path}/feature_validation/{feature_name}/performance")

    excel_writer = pd.ExcelWriter(os.path.join(rf"{save_path}/feature_validation/{feature_name}/performance", "feature_importance.xlsx"))
    feature_importance.to_excel(excel_writer, sheet_name="all")
    excel_writer.save()
    excel_writer.close()

    file_path = rf"{save_path}/feature_validation/{feature_name}/performance/by_year_ror.json"  # 指定要保存JSON的文件路径
    with open(file_path, "w") as json_file:
        json.dump(ror_info, json_file)

    file_path = rf"{save_path}/feature_validation/{feature_name}/performance/by_year_md.json"
    with open(file_path, "w") as json_file:
        json.dump(md_info, json_file)
    #
    if ((max_ic_ >= 0.03) and (rank_corr_ >= 0.6) and (max_ror_ >= 0.08)) or (max_extra_ror_ >= 0.2):#两个筛选条件分别筛选线性因子和非线性因子
        if not os.path.exists(os.path.join(save_path, "feature_value")):
            os.makedirs(os.path.join(save_path, "feature_value"))
        feature_df.to_csv(os.path.join(save_path, "feature_value", "%s.csv" % feature_name))
        print(
            "rank_ic_mean_series", final_res["rank_ic_mean_series"].values.tolist(), "\n",
            "bkt_rank_corr", final_res["bkt_rank_corr"], "\n",
            "bkt_annual_ror_series", final_res["bkt_annual_ror_series"].values.tolist(), "\n",
            "bkt_extra_annual_ror_series", final_res["bkt_extra_annual_ror_series"].values.tolist(), "\n",
            "turnover_mean_series", final_res["turnover_mean_series"].values.tolist(), "\n",
            "bkt_extra_annual_ror_series_fee", final_res["bkt_extra_annual_ror_series_fee"].values.tolist(), "\n",
            "ror_info", ror_info, "\n",
            "md_info", md_info,
        )
        print("\n" + "pass, single task over", time.time() - start_ts)
        return final_res, True
    else:
        print("\n" + "not pass, single task over", time.time() - start_ts)
        return final_res, False


def feature_normalization_by_df(feature_df, mvl=15):#去极值和标准化
    # 中位数拉回法去极值
    median_series = feature_df.median(axis=1)
    med_series = feature_df.sub(median_series, axis=0).abs().median(axis=1)
    upper_bound_series = median_series + mvl * med_series
    lower_bound_series = median_series - mvl * med_series
    feature_df = feature_df.clip(lower=lower_bound_series, upper=upper_bound_series, axis=0)#经典截断操作
    # 将空值填充为零，并做Z-score标准化
    return feature_df.sub(feature_df.mean(axis=1), axis=0).div(feature_df.std(axis=1), axis=0).replace([np.nan], 0.0)


def load_single_feature_df(args):
    start_ts = time.time()
    feature_file_name, feature_path, x_mean_lag_list, start_date, end_date, is_x_norm = args
    if is_x_norm == 1:
        feature_path = r"E:/raw_data2/x_data/feature_value_Z_score"
    elif is_x_norm == 2:
        feature_path = r"E:/raw_data2/x_data/feature_value_size_neutralization"
    elif is_x_norm == 3:
        feature_path = r"E:/raw_data2/x_data/feature_value_size_industry_neutralization"
    elif is_x_norm == 4:
        feature_path = r"E:/raw_data2/x_data/feature_value_rank"
    feature_name = feature_file_name.split(".parquet")[0]
    feature_df = df_read_parquet(os.path.join(feature_path, feature_file_name)).T
    if len(feature_df) == 0:
        return []
    feature_df = feature_df[(feature_df.index >= start_date) & (feature_df.index <= end_date)].sort_index(ascending=True).T#此前已做过转置操作，index变为日期，再转置一次获得指定时间窗口内的数据
    feature_df_list = []
    for x_mean_lag in x_mean_lag_list:
        m1_feature_df = feature_df.T.rolling(x_mean_lag, min_periods=1).mean().T#取时序均值
        m1_feature_df.columns = pd.MultiIndex.from_arrays([["%s_km%d" % (feature_name, x_mean_lag)] * len(m1_feature_df.columns), m1_feature_df.columns])
        feature_df_list.append(m1_feature_df)
    sys.stdout.write("\r" + f"{feature_name} {time.time() - start_ts}")
    gc.collect()
    return feature_df_list


def load_raw_x_data(feature_path, feature_list, x_mean_lag_list, start_date, end_date, is_x_norm):
    feature_df_list = []
    for i_ in sorted(feature_list):
        feature_df_list.extend(load_single_feature_df((i_, feature_path, x_mean_lag_list, start_date, end_date, is_x_norm)))
    gc.collect()
    raw_x_df = pd.concat(feature_df_list, axis=1, ignore_index=False, sort=True).stack(level=1)
    return raw_x_df


def load_raw_y_data(y_type, y_lag_list, quote_path, start_date, end_date):
    is_st = df_read_parquet(os.path.join(quote_path, "stock_label", "is_st.parquet"))
    list_day_num = df_read_parquet(os.path.join(quote_path, "stock_label", "list_day_num.parquet"))
    filter_df = (is_st == 0) * (list_day_num >= 120) #六个月滚动训练窗口
    if y_type == "z":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "mean":#超额收益率
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "reg_industry":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "industry_rank":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "reg_index":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "index_rank":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "rank":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%s_%d.parquet" % ("oto", y_type, y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        # TODO:
        # y_df = y_df.rank(axis=0, pct=True) * 3000
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "rank_pct":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_rank_%d.parquet" % ("oto", y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        y_df = y_df.rank(axis=0, pct=True)
        is_valid_y = y_df.mask(filter_df != 1.0)
    elif y_type == "rank_pct_3000":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_rank_%d.parquet" % ("oto", y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        y_df = y_df.rank(axis=0, pct=True) * 3000
        is_valid_y = y_df.mask(filter_df != 1.0) 
    elif y_type == "None":
        y_df = df_read_parquet(os.path.join(quote_path, "pred_y", "%s_%d.parquet" % ("oto", y_lag_list[0]))).T
        y_df = y_df[(y_df.index >= start_date) & (y_df.index <= end_date)].T
        is_valid_y = y_df.mask(filter_df != 1.0)
    is_valid_y.columns = pd.MultiIndex.from_arrays([["%s_%d" % ("oto", y_lag_list[0])] * len(is_valid_y.columns), is_valid_y.columns])
    is_valid_y.stack()
    return is_valid_y.stack()


def XGB_bayesian(train_data, x_features, max_depth, learning_rate, subsample, min_child_weight):
    param = {
        'max_depth': int(max_depth),
        'learning_rate': learning_rate,
        'subsample': subsample,
        'min_child_weight': min_child_weight,
        'objective': 'reg:squarederror',
        'seed': 19950413,
        'n_estimators': 100,
        'eval_metric': 'mse'
    }
    x = train_data[x_features]
    y = train_data["y"]

    train_model_pred = np.zeros(len(train_data))

    n_cv = 5
    kf = StratifiedKFold(n_splits=n_cv, shuffle=True, random_state=666)
    mse_list = []
    for train_index, valid_index in kf.split(x, y):
        train, valid = train_data.iloc[train_index], train_data.iloc[valid_index]

        model = xgb.XGBRegressor(**param)
        model.fit(train[x_features], train["y"], eval_set=[(valid[x_features], valid["y"])],
                  early_stopping_rounds=10, verbose=0)

        train_model_pred[valid_index] = model.predict(valid[x_features])
        mse_list.append(mean_squared_error(valid["y"], train_model_pred))

    return np.mean(mse_list)


def gen_pred_y(join_df, raw_y_series, max_y_lag, pred_y_lag, update_lag, sample_interval, sample_days, model_prm, save_path):
    x_features = sorted(join_df.columns)
    raw_y_series = raw_y_series.reindex(join_df.index)
    join_df["y"] = raw_y_series
    # 默认第一年不用
    # 滚动训练 + 训练样本时段 + 采样方式
    all_trade_days = sorted(list(set(join_df.index.get_level_values(1))))
    sample_array = np.zeros(len(all_trade_days))
    # 每隔 sample_intervals 设置一个值为 1
    for i in range(0, len(all_trade_days), sample_interval):
        sample_array[i] = 1
    sample_series = pd.Series(sample_array, index=all_trade_days)
    update_index_list = sorted(list(set(list(np.arange(sample_days, len(all_trade_days), update_lag)) + [len(all_trade_days) - 1] + [0])))
    pred_y_df_list = []
    if model_prm["name"] == "linear":
        coefficients_df_list = []
    else:
        feature_importance_list = []
    start_ts = time.time()
    for update_index_ in range(len(update_index_list)):
        if (update_index_ == 0) or (update_index_ == len(update_index_list) - 1):
            continue
        # 左开右闭
        test_start_date = all_trade_days[update_index_list[update_index_] + 1]
        test_end_date = all_trade_days[update_index_list[update_index_ + 1]]
        if sample_days > 0:
            train_start_date = all_trade_days[update_index_list[update_index_] - sample_days]
        else:
            train_start_date = all_trade_days[0]
        train_end_date = all_trade_days[update_index_list[update_index_] - max_y_lag - 1]
        train_index_filter = np.multiply(
            join_df.index.get_level_values(1) >= train_start_date,
            join_df.index.get_level_values(1) <= train_end_date,
            sample_series[join_df.index.get_level_values(1)] == 1
        )
        test_index_filter = np.multiply(
            join_df.index.get_level_values(1) >= test_start_date,
            join_df.index.get_level_values(1) <= test_end_date,
        )
        train_df = join_df[train_index_filter].dropna(how="any")
        print(train_df)
        test_df = join_df.loc[test_index_filter]
        x_ = test_df[x_features].values.astype(np.float64)
        x_[np.isnan(x_)] = 0.0

        #
        if model_prm["name"] == "xgb":
            model_ = xgb.XGBRegressor(
                objective='reg:squarederror',
                learning_rate=model_prm["learning_rate"],
                max_depth=model_prm["max_depth"],
                n_estimators=model_prm["n_estimators"],
                min_child_weight=model_prm["min_child_weight"],
            )
            model_.fit(X=train_df[x_features].values.astype(np.float64), y=train_df["y"].values.astype(np.float64))
            # XGB_BO = BayesianOptimization(
            #     lambda max_depth, learning_rate, subsample, min_child_weight: XGB_bayesian(
            #         train_df, x_features, max_depth, learning_rate, subsample, min_child_weight
            #     ),
            #     {
            #         "max_depth": model_prm["max_depth"],
            #         "subsample": model_prm["subsample"],
            #         "learning_rate": model_prm["learning_rate"],
            #         "min_child_weight": model_prm["min_child_weight"]
            #     },
            #     random_state=666
            # )
            # init_points = 10
            # n_iter = 50
            # with warnings.catch_warnings():
            #     warnings.filterwarnings('ignore')
            #     XGB_BO.minimize(init_points=init_points, n_iter=n_iter, acq='ucb', xi=0.0, alpha=1e-6)

            feature_importance = pd.DataFrame(model_.feature_importances_, index=[x_features], columns=[str(update_index_)])
            feature_importance_list.append(feature_importance)

            pred_y_df_ = pd.Series(model_.predict(x_), index=test_df.index).unstack(level=1)
            pred_y_df_list.append(pred_y_df_)
        elif model_prm["name"] == "lgbm":
            #lgbm的train函数默认使用L2损失函数，也就是均方误差
            model_ = lgb.train(params=model_prm, train_set=lgb.Dataset(train_df[x_features].values.astype(np.float64), label=train_df["y"].values.astype(np.float64)))

            # feature_importance = pd.DataFrame(model_.feature_importance(), index=[x_features], columns=[str(update_index_)])
            feature_importance = pd.DataFrame(model_.feature_importance(importance_type='gain'), index=[x_features], columns=[str(update_index_)])
            feature_importance_list.append(feature_importance)

            pred_y_df_ = pd.Series(model_.predict(x_), index=test_df.index).unstack(level=1)
            pred_y_df_list.append(pred_y_df_)
        elif model_prm["name"] == "rf":
            rf = RandomForestRegressor(
                n_estimators=model_prm["n_estimators"],
                max_depth=model_prm["max_depth"],
                n_jobs=model_prm["n_jobs"]
            )
            rf.fit(X=train_df[x_features].values.astype(np.float64), y=train_df["y"].values.astype(np.float64))

            feature_importance = pd.DataFrame(rf.feature_importances_, index=[x_features], columns=[str(update_index_)])
            feature_importance_list.append(feature_importance)

            pred_y_df_ = pd.Series(rf.predict(x_), index=test_df.index).unstack(level=1)
            pred_y_df_list.append(pred_y_df_)
        elif model_prm["name"] == "linear":
            model_ = LinearRegression()
            model_.fit(X=train_df[x_features].values.astype(np.float64), y=train_df["y"].values.astype(np.float64))

            coefficients_df = pd.DataFrame(model_.coef_, index=[x_features], columns=[str(update_index_)])
            # 存储每一期的回归系数
            coefficients_df_list.append(coefficients_df)

            pred_y_df_ = pd.Series(model_.predict(x_), index=test_df.index).unstack(level=1)
            pred_y_df_list.append(pred_y_df_)
        else:
            pred_y_df_ = pd.Series(np.nanmean(x_, axis=1), index=test_df.index).unstack(level=1)
            pred_y_df_list.append(pred_y_df_)

    if model_prm["name"] == "linear":
        feature_importance_all = pd.concat(coefficients_df_list, axis=1, ignore_index=False, sort=True)
    else:
        feature_importance_all = pd.concat(feature_importance_list, axis=1, ignore_index=False, sort=True)

    final_pred_y_ = pd.concat(pred_y_df_list, axis=1, ignore_index=False, sort=True).T
    final_pred_y_ = final_pred_y_.sort_index(ascending=True).T
    sys.stdout.write("\r" + model_prm["name"] + f" {time.time() - start_ts}")
    # 对y进行操作
    return final_pred_y_.T.rolling(pred_y_lag, min_periods=1).mean().T, feature_importance_all


def pred_y_validation(pred_y_df, y_name, save_path, quote_path, feature_importance, pool_benchmark="", ror_benchmark=""):
    test_given_feature(
        feature_name=y_name,
        feature_df=pred_y_df,
        wrk_path=quote_path,
        group_num=10,
        holding_period=1,
        is_normalization=1,
        feature_importance=feature_importance,
        pool_benchmark=pool_benchmark,
        ror_benchmark=ror_benchmark,
        y_type="oto",
        save_path=save_path
    )
    return None


def test_model_mean_weight_combo():
    # wrk_path = r"E:\gd_feature_combo1\feature_value"
    # feature_df_list = []
    # for csv_file_name in sorted(os.listdir(wrk_path)):
    #     if csv_file_name.split("_")[0] not in ["mean"]:
    #         continue
    #     feature_df = pd.read_csv("%s/%s" % (wrk_path, csv_file_name), index_col=0)
    #     feature_df = feature_normalization_by_df(feature_df.T, mvl=15)
    #     feature_df.columns = pd.MultiIndex.from_arrays([feature_df.columns, [csv_file_name[:-4]] * len(feature_df.columns)])
    #     feature_df_list.append(feature_df)
    # final_feature_df = pd.concat(feature_df_list, axis=1, ignore_index=False, sort=True).stack(level=0).mean(axis=1).unstack()
    final_feature_df = pd.read_csv("D:/df_y_pred_alpha_feature_linear_all.csv", index_col=0)
    test_given_feature(
        feature_name="combo",
        feature_df=final_feature_df.T,
        wrk_path=r"D:/alpha_data",
        group_num=10,
        holding_period=1,
        is_normalization=1,
        # pool_benchmark="000300.SH",
        # ror_benchmark="",
        y_type="oto",
        save_path="E:/lzy_feature_combo1"
    )
    return None


def model_training():
    _feature_path = r"E:/raw_data2/x_data/feature_value"
    _quote_path = r"D:/alpha_data"
    _start_date = "2018-05-01"
    _end_date = "2024-05-01"
    _save_path = r"E:/lzy_feature_combo"
    all_feature_list = os.listdir(_feature_path)
    for _x_mean_lag_list, _is_x_norm in list(product([[5]], [0,1,2,3,4])):
        _x_raw_df = pd.DataFrame()
        gc.collect()
        _x_raw_df = load_raw_x_data(
            feature_path=_feature_path,
            feature_list=all_feature_list,
            x_mean_lag_list=_x_mean_lag_list,
            start_date=_start_date,
            end_date=_end_date,
            is_x_norm=_is_x_norm
        )
        _x_label = "xm%s-n%d" % ("-".join([str(i) for i in _x_mean_lag_list]), _is_x_norm)#用于生成名称
        for _y_type, _y_lags in list(product(["rank","z","mean","reg_industry","industry_rank","reg_index","index_rank","None","rank_pct","rank_pct_3000"], [[5]])):
        # for _y_type, _y_lags in list(product(["rank_pct","rank_pct_3000"], [[5]])):
            _y_series = load_raw_y_data(
                y_type=_y_type, y_lag_list=_y_lags, quote_path=_quote_path, start_date=_start_date, end_date=_end_date
            )
            _y_label = "y%s-%s" % (_y_type, "-".join([str(i) for i in _y_lags]))#用于生成名称
            for _model_prm in [
                # 初始参数为相对最优参数
                # {"name": "mean"},
                # {"name": "linear"},
                # {"name": "xgb", "n_estimators": 100, "max_depth": 6, "learning_rate": 0.005, "min_child_weight": 100},
                {"name": "xgb", "n_estimators": 100, "max_depth": 4, "learning_rate": 0.01, "min_child_weight": 100},
                # {"name": "lgbm", "n_estimators": 100, "max_depth": 6, "learning_rate": 0.005, "num_leaves": 10},
                {"name": "lgbm", "n_estimators": 100, "max_depth": 4, "learning_rate": 0.01, "num_leaves": 10},
                # {"name": "xgb", "n_estimators": 100, "max_depth": [4,5,6,7,8,9,10], "subsample":[0.6,0.65,0.7,0.75,0.8,0.85,0.9,0.95,1],"learning_rate": (0.001, 0.01), "min_child_weight": (1,100)},
                # {"name": "rf", "n_estimators": 100, "max_depth": 4, "n_jobs": 30},
                # {"name": "rf", "n_estimators": 100, "max_depth": 6, "n_jobs": 30}
            ]:
                for _sample_days, _sample_interval, _update_lag, _y_mean_lag in [[895, 5, 1100, 5]]:
                    # _sample_interval  跨时段采样
                    # if _update_lag == 180:
                    #     gap = "rolling_nine_month_window"
                    # else:
                    #     gap = "rolling_one_year_window"
                    # depth = _model_prm["max_depth"]
                    name = _model_prm["name"]
                    _save_path = rf"E:/rolling_sampling_lzy_feature_combo_fixed_window_{name}_{_sample_interval}"
                    _pred_y, feature_importance = gen_pred_y(
                        join_df=copy.deepcopy(_x_raw_df),
                        raw_y_series=copy.deepcopy(_y_series),
                        max_y_lag=np.max(_y_lags),
                        pred_y_lag=_y_mean_lag,
                        update_lag=_update_lag,
                        sample_interval=_sample_interval,
                        sample_days=_sample_days,
                        model_prm=_model_prm,
                        save_path=_save_path
                    )
                    pred_y_validation(
                        pred_y_df=_pred_y,
                        y_name="_".join([_model_prm["name"], _x_label, _y_label, "-".join([str(_sample_days), str(_sample_interval), str(_update_lag), str(_y_mean_lag)])]),
                        save_path=_save_path,
                        quote_path=_quote_path,
                        feature_importance=feature_importance,
                        ror_benchmark="000852.SH"
                    )
    return None


if __name__ == "__main__":
    # test_model_mean_weight_combo()
    model_training()


"""
目标： 中证1000 2022-2023年化25% 最大回撤2%-3%; 2024年最大回撤10%, 上半年8% 

1200 + 1000 = 2200 


2022-2023  费后年化 30%
2024       费后 1%   回测  20%


1. 下载因子 ---  然后对因子进行标准化  ---  在进行行业市值中性化 stock_d_quote/sw_industry  style_size 

    2. 模型训练   -----   筛选因子
    
        1) 分年段统计重要度，最终选择样本内重要度得分较高的因子  ---   全因子部分样本
    
        2） PCA -- 特征过多，太慢
        
        3） 类似于rf的随机采特征, ----  全样本内部分因子   
    
        4）  
    
    3. 模型绩效输出   ------  通过上述选择的因子,以2019-2021三年为样本内训练模型，观测2022-之后的表现 

"""

"""
特征命名含义
n0 --- 原始因子值
n1 --- Z_score
n2 --- size_neutralization
n3 --- size_industry_neutralization
n4 --- rank
"""
