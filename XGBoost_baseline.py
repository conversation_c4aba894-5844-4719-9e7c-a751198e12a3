# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import time
from pandas.tseries.offsets import BDay
from tqdm import tqdm
import xgboost as xgb
from sklearn.metrics import log_loss
from sklearn.metrics import f1_score
from sklearn.multioutput import MultiOutputRegressor
from sklearn.linear_model import LinearRegression
from PyEMD import EMD
from bayes_opt import BayesianOptimization
import os
import ray
from scipy.stats import rankdata
from scipy.stats import skew
os.environ["OMP_NUM_THREADS"] = "24"

'''
cd lzy_T0_alpha
运行脚本: python3 XGBoost_baseline.py >> ~/.bash_logout 2>&1
忽略挂断信号：nohup python3 XGBoost_baseline.py >> ~/.bash_logout 2>&1 &
杀死进程：pkill -u jc_intern
'''

class feature_():
    def __init__(self, data, kwargs):
        self.df = data
        self.df['endtime'] = pd.to_datetime(self.df['endtime'])
        self.backward_start_minute = kwargs['backward_start_minute']
        self.backward_end_minute = kwargs['backward_end_minute']
        self.predict_type = kwargs['predict_type']
        self.forward_start_minute = kwargs['forward_start_minute']
        self.forward_end_minute = kwargs['forward_end_minute']
        self.feature_df = pd.DataFrame()
        self.feature_df['endtime'] = self.df['endtime']
    
    def _time_interval(self):
        """
        处理回溯区间和预测区间的时间戳：
           回溯时间开始时间：current_time - backward_start_minute
           回溯时间结束时间：current_time - backward_end_minute
           出现异常：早盘开盘前（< 09:25:00）
                    午盘开盘前（< 13:00:00 &> 11:30:00）

           预测时间开始时间：current_time + forward_start_minute
           预测时间结束时间：current_time + forward_end_minute
           出现异常：早盘休市后（> 11:30:00 &< 13:00:00）
                    午盘休市后（> 15:00:00）
        """
        #回溯区间
        self.df['backward_start_time'] = self.df['endtime'] - pd.to_timedelta(self.backward_start_minute, unit='min')
        self.df['backward_end_time'] = self.df['endtime'] - pd.to_timedelta(self.backward_end_minute, unit='min')
        
        # 处理早盘开盘前的情况 (< 09:25:00)
        before_morning_mask = (self.df['backward_start_time'].dt.time < pd.to_datetime('09:25:00').time())
        prev_day = self.df.loc[before_morning_mask, 'backward_start_time'].dt.normalize() - BDay(1)
        current_time = self.df.loc[before_morning_mask, 'backward_start_time'].dt.time
        correct_morning_time = pd.to_timedelta('15:00:00') - (pd.to_timedelta('09:25:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_morning_mask, 'backward_start_time'] = prev_day + correct_morning_time
    
        before_morning_mask = (self.df['backward_end_time'].dt.time < pd.to_datetime('09:25:00').time())
        prev_day = self.df.loc[before_morning_mask, 'backward_end_time'].dt.normalize() - BDay(1)
        current_time = self.df.loc[before_morning_mask, 'backward_end_time'].dt.time
        correct_morning_time = pd.to_timedelta('15:00:00') - (pd.to_timedelta('09:25:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_morning_mask, 'backward_end_time'] = prev_day + correct_morning_time

        # 处理午盘开盘前的情况 (< 13:00:00 &> 11:30:00)
        before_afternoon_mask = ((self.df['backward_start_time'].dt.time < pd.to_datetime('13:00:00').time()) & 
                          (self.df['backward_start_time'].dt.time > pd.to_datetime('11:30:00').time()))
        current_time = self.df.loc[before_afternoon_mask, 'backward_start_time'].dt.time
        correct_afternoon_time = pd.to_timedelta('11:30:00') - (pd.to_timedelta('13:00:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_afternoon_mask, 'backward_start_time'] = self.df.loc[before_afternoon_mask, 'backward_start_time'].dt.normalize() + correct_afternoon_time
        
        before_afternoon_mask = ((self.df['backward_end_time'].dt.time < pd.to_datetime('13:00:00').time()) & 
                          (self.df['backward_end_time'].dt.time > pd.to_datetime('11:30:00').time()))
        current_time = self.df.loc[before_afternoon_mask, 'backward_end_time'].dt.time
        correct_afternoon_time = pd.to_timedelta('11:30:00') - (pd.to_timedelta('13:00:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_afternoon_mask, 'backward_end_time'] = self.df.loc[before_afternoon_mask, 'backward_end_time'].dt.normalize() + correct_afternoon_time
        
        if not self.predict_type == 'five_class':
            #预测区间
            self.df['forward_start_time'] = self.df['endtime'] + pd.to_timedelta(self.forward_start_minute, unit='min')
            self.df['forward_end_time'] = self.df['endtime'] + pd.to_timedelta(self.forward_end_minute, unit='min')

            # 处理早盘休市后的情况 (> 11:30:00 &< 13:00:00)
            after_morning_mask = ((self.df['forward_start_time'].dt.time > pd.to_datetime('11:30:00').time()) & 
                              (self.df['forward_start_time'].dt.time < pd.to_datetime('13:00:00').time()))
            current_time = self.df['forward_start_time'].dt.time
            correct_morning_time = pd.to_timedelta('13:00:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                  pd.to_timedelta('11:30:00'))
            self.df.loc[after_morning_mask, 'forward_start_time'] = self.df.loc[after_morning_mask, 'forward_start_time'].dt.normalize() + correct_morning_time

            after_morning_mask = ((self.df['forward_end_time'].dt.time > pd.to_datetime('11:30:00').time()) & 
                              (self.df['forward_end_time'].dt.time < pd.to_datetime('13:00:00').time()))
            current_time = self.df['forward_end_time'].dt.time
            correct_morning_time = pd.to_timedelta('13:00:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                  pd.to_timedelta('11:30:00'))
            self.df.loc[after_morning_mask, 'forward_end_time'] = self.df.loc[after_morning_mask, 'forward_end_time'].dt.normalize() + correct_morning_time 

            # 处理午盘休市后的情况 (> 15:00:00)
            after_afternoon_mask = (self.df['forward_start_time'].dt.time > pd.to_datetime('15:00:00').time())
            next_day = self.df.loc[after_afternoon_mask, 'forward_start_time'].dt.normalize() + BDay(1)
            current_time = self.df.loc[after_afternoon_mask, 'forward_start_time'].dt.time
            correct_afternoon_time = pd.to_timedelta('09:25:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                    pd.to_timedelta('15:00:00'))
            self.df.loc[after_afternoon_mask, 'forward_start_time'] = next_day + correct_afternoon_time

            after_afternoon_mask = (self.df['forward_end_time'].dt.time > pd.to_datetime('15:00:00').time())
            next_day = self.df.loc[after_afternoon_mask, 'forward_end_time'].dt.normalize() + BDay(1)
            current_time = self.df.loc[after_afternoon_mask, 'forward_end_time'].dt.time
            correct_afternoon_time = pd.to_timedelta('09:25:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                    pd.to_timedelta('15:00:00'))
            self.df.loc[after_afternoon_mask, 'forward_end_time'] = next_day + correct_afternoon_time
        return None
    
        '''
        从统计域、谱域和时域的角度出发，共容纳数十种特征提取方法：
        （1）基于统计域的时序特征包含：最大值、最小值、均值、中位数、偏度、峰度、直方图、四分位距、
        绝对误差均值、绝对误差中位数、均方根、标准差、方差、经验分布函数百分位数、经验分布函数斜率。
        （2）基于谱域的时序特征包含：快速傅里叶变换、傅里叶变换平均系数、小波变换、小波绝对值、小波标准差、
        小波方差、谱距离、频谱基频、频谱最大频率、频谱中域、频谱最大峰值。
        （3）基于时域的时序特征包含：自相关、质心、差分均值、差分绝对值均值、差分中位数、差分绝对值中位数、
        差分绝对值之和、熵、波峰与波谷距离、曲线覆盖面积、最大峰值个数、最小峰值个数、跨零率
        '''

    def factor_transaction_generation(self):
        '''
        预期大额或者频繁的交易可能会在短期内持续存在：（持续补充）
            （1）广度因子：回溯区间内的成交笔数（数据不足）
            （2）即时性因子：回溯区间内每笔成交的平均间隔时间（数据不足）
            （3）平均成交量因子：回溯区间内的每个tick的平均成交量
            （3）总成交量因子：回溯区间内总成交量
            （4）最大成交量因子：回溯区间内单个tick最大成交量
            （5）最小成交量因子：回溯区间内单个tick最小成交量
            （6）价格最高成交量因子：回溯区间内单个tick价格最高成交量
            （7）价格最低成交量因子：回溯区间内单个tick价格最低成交量
            （8）成交量标准差因子：回溯区间内成交量的标准差
            （9）成交量偏度因子：回溯区间内成交量的偏度
            （10）成交量峰度因子：回溯区间内成交量的峰度

            （11）平均买入成交量因子：回溯区间内总卖出成交量
            （12）总买入成交量因子：回溯区间内总买入成交量
            （13）最大买入成交量因子：回溯区间内单个tick最大买入成交量
            （14）最小买入成交量因子：回溯区间内单个tick最小买入成交量
            （15）价格最高买入成交量因子：回溯区间内单个tick价格最高买入成交量
            （16）价格最低买入成交量因子：回溯区间内单个tick价格最低买入成交量
            （17）买入成交量标准差因子：回溯区间内买入成交量的标准差
            （18）买入成交量偏度因子：回溯区间内买入成交量的偏度
            （19）买入成交量峰度因子：回溯区间内买入成交量的峰度
            
            （20）平均卖出成交量因子：回溯区间内总卖出成交量
            （21）总卖出成交量因子：回溯区间内总卖出成交量
            （22）最大卖出成交量因子：回溯区间内单个tick最大卖出成交量
            （23）最小卖出成交量因子：回溯区间内单个tick最小卖出成交量
            （24）价格最高卖出成交量因子：回溯区间内单个tick价格最高卖出成交量
            （25）价格最低卖出成交量因子：回溯区间内单个tick价格最低卖出成交量
            （26）卖出成交量标准差因子：回溯区间内卖出成交量的标准差
            （21）卖出成交量偏度因子：回溯区间内卖出成交量的偏度
            （22）卖出成交量峰度因子：回溯区间内卖出成交量的峰度

            （23）上行总成交量因子：回溯区间内上行tick的总成交量
            （24）上行平均成交量因子：回溯区间内上行tick的平均成交量
            （25）上行最大成交量因子：回溯区间内上行tick的最大成交量
            （26）上行最小成交量因子：回溯区间内上行tick的最小成交量
            （27）上行价格最高成交量因子：回溯区间内上行tick价格最高成交量
            （28）上行价格最低成交量因子：回溯区间内上行tick价格最低成交量
            （29）上行成交量标准差因子：回溯区间内上行tick成交量的标准差
            （30）上行成交量偏度因子：回溯区间内上行tick成交量的偏度
            （31）上行成交量峰度因子：回溯区间内上行tick成交量的峰度

            （32）上行总买入成交量因子：回溯区间内上行tick的总买入成交量
            （33）上行平均买入成交量因子：回溯区间内上行tick的平均买入成交量
            （34）上行最大买入成交量因子：回溯区间内上行tick的最大买入成交量
            （35）上行最小买入成交量因子：回溯区间内上行tick的最小买入成交量
            （36）上行价格最高买入成交量因子：回溯区间内上行tick价格最高买入成交量
            （37）上行价格最低买入成交量因子：回溯区间内上行tick价格最低买入成交量
            （38）上行买入成交量标准差因子：回溯区间内上行tick买入成交量的标准差
            （39）上行买入成交量偏度因子：回溯区间内上行tick买入成交量的偏度
            （40）上行买入成交量峰度因子：回溯区间内上行tick买入成交量的峰度
            
            （41）上行总卖出成交量因子：回溯区间内下行tick的总卖出成交量
            （42）上行平均卖出成交量因子：回溯区间内下行tick的平均卖出成交量
            （43）上行最大卖出成交量因子：回溯区间内下行tick的最大卖出成交量
            （44）上行最小卖出成交量因子：回溯区间内下行tick的最小卖出成交量
            （45）上行价格最高卖出成交量因子：回溯区间内下行tick价格最高卖出成交量
            （46）上行价格最低卖出成交量因子：回溯区间内下行tick价格最低卖出成交量
            （47）上行卖出成交量标准差因子：回溯区间内下行tick卖出成交量的标准差
            （48）上行卖出成交量偏度因子：回溯区间内下行tick卖出成交量的偏度
            （49）上行卖出成交量峰度因子：回溯区间内下行tick卖出成交量的峰度

            （50）下行总成交量因子：回溯区间内下行tick的总成交量
            （51）下行平均成交量因子：回溯区间内下行tick的平均成交量
            （52）下行最大成交量因子：回溯区间内下行tick的最大成交量
            （53）下行最小成交量因子：回溯区间内下行tick的最小成交量
            （54）下行价格最高成交量因子：回溯区间内下行tick价格最高成交量
            （55）下行价格最低成交量因子：回溯区间内下行tick价格最低成交量
            （56）下行成交量标准差因子：回溯区间内下行tick成交量的标准差
            （57）下行成交量偏度因子：回溯区间内下行tick成交量的偏度
            （58）下行成交量峰度因子：回溯区间内下行tick成交量的峰度

            （59）下行总买入成交量因子：回溯区间内下行tick的总买入成交量
            （60）下行平均买入成交量因子：回溯区间内下行tick的平均买入成交量
            （61）下行最大买入成交量因子：回溯区间内下行tick的最大买入成交量
            （62）下行最小买入成交量因子：回溯区间内下行tick的最小买入成交量
            （63）下行价格最高买入成交量因子：回溯区间内下行tick价格最高买入成交量
            （64）下行价格最低买入成交量因子：回溯区间内下行tick价格最低买入成交量
            （65）下行买入成交量标准差因子：回溯区间内下行tick买入成交量的标准差
            （66）下行买入成交量偏度因子：回溯区间内下行tick买入成交量的偏度

            （67）下行总卖出成交量因子：回溯区间内下行tick的总卖出成交量
            （68）下行平均卖出成交量因子：回溯区间内下行tick的平均卖出成交量
            （69）下行最大卖出成交量因子：回溯区间内下行tick的最大卖出成交量
            （70）下行最小卖出成交量因子：回溯区间内下行tick的最小卖出成交量
            （71）下行价格最高卖出成交量因子：回溯区间内下行tick价格最高卖出成交量
            （72）下行价格最低卖出成交量因子：回溯区间内下行tick价格最低卖出成交量
            （73）下行卖出成交量标准差因子：回溯区间内下行tick卖出成交量的标准差
            （74）下行卖出成交量偏度因子：回溯区间内下行tick卖出成交量的偏度
            （75）下行卖出成交量峰度因子：回溯区间内下行tick卖出成交量的峰度
            日期7.24
            （76）成交量分桶熵因子：等距分桶后求熵
                成交量分桶熵衡量了成交量不稳定性的分散程度，因子值越大，说明该股在最近一段时间成交量不稳定性的分散程度较大，
                下行风险较大
            
        '''
        factor_name = ['average_volume','total_volume','max_volume','min_volume',
                        'price_max_volume','price_min_volume',
                        'std_volume','skew_volume','kurt_volume',
                        'average_buy_volume','total_buy_volume','max_buy_volume','min_buy_volume',
                        'price_max_buy_volume','price_min_buy_volume',
                        'std_buy_volume','skew_buy_volume','kurt_buy_volume',
                        'average_sell_volume','total_sell_volume','max_sell_volume','min_sell_volume',
                        'price_max_sell_volume','price_min_sell_volume',
                        'std_sell_volume','skew_sell_volume','kurt_sell_volume',
                        
                        'up_average_volume','up_total_volume','up_max_volume',
                        'up_min_volume','up_price_max_volume','up_price_min_volume',
                        'up_std_volume','up_skew_volume','up_kurt_volume',
                        'up_average_buy_volume','up_total_buy_volume', 'up_max_buy_volume', 'up_min_buy_volume', 
                        'up_price_max_buy_volume', 'up_price_min_buy_volume', 
                        'up_std_buy_volume', 'up_skew_buy_volume','up_kurt_buy_volume',
                        'up_average_sell_volume','up_total_sell_volume', 'up_max_sell_volume', 'up_min_sell_volume', 
                        'up_price_max_sell_volume', 'up_price_min_sell_volume', 
                        'up_std_sell_volume', 'up_skew_sell_volume','up_kurt_sell_volume',
                        
                        'down_average_volume','down_total_volume','down_max_volume',
                        'down_min_volume','down_price_max_volume','down_price_min_volume',
                        'down_std_volume','down_skew_volume','down_kurt_volume',
                        'down_average_buy_volume','down_total_buy_volume', 'down_max_buy_volume', 'down_min_buy_volume', 
                        'down_price_max_buy_volume', 'down_price_min_buy_volume', 
                        'down_std_buy_volume', 'down_skew_buy_volume','down_kurt_buy_volume',
                        'down_average_sell_volume','down_total_sell_volume', 'down_max_sell_volume', 'down_min_sell_volume', 
                        'down_price_max_sell_volume', 'down_price_min_sell_volume', 
                        'down_std_sell_volume', 'down_skew_sell_volume','down_kurt_sell_volume',
                        
                        'binned_entropy',]
        
        for row in tqdm(self.df.index):
            mask = ((self.df['endtime'] >= self.df.loc[row, 'backward_start_time']) & 
                    (self.df['endtime'] <= self.df.loc[row, 'backward_end_time']))
            temp = self.df[mask]
            if not temp.empty:
                self.feature_df.loc[row, 'average_volume'] = temp['vol'].mean()
                self.feature_df.loc[row, 'total_volume'] = temp['vol'].sum()
                self.feature_df.loc[row, 'max_volume'] = temp['vol'].max()
                self.feature_df.loc[row, 'min_volume'] = temp['vol'].min()
                self.feature_df.loc[row, 'price_max_volume'] = temp.loc[temp['close'].idxmax(), 'vol']
                self.feature_df.loc[row, 'price_min_volume'] = temp.loc[temp['close'].idxmin(), 'vol']
                self.feature_df.loc[row, 'std_volume'] = temp['vol'].std()
                self.feature_df.loc[row, 'skew_volume'] = temp['vol'].skew()
                self.feature_df.loc[row, 'kurt_volume'] = temp['vol'].kurt()

                self.feature_df.loc[row, 'average_buy_volume'] = temp['bc1'].mean()
                self.feature_df.loc[row, 'total_buy_volume'] = temp['bc1'].sum()
                self.feature_df.loc[row, 'max_buy_volume'] = temp['bc1'].max()
                self.feature_df.loc[row, 'min_buy_volume'] = temp['bc1'].min()
                self.feature_df.loc[row, 'price_max_buy_volume'] = temp.loc[temp['bc1'].idxmax(), 'bc1']
                self.feature_df.loc[row, 'price_min_buy_volume'] = temp.loc[temp['bc1'].idxmin(), 'bc1']
                self.feature_df.loc[row, 'std_buy_volume'] = temp['bc1'].std()
                self.feature_df.loc[row, 'skew_buy_volume'] = temp['bc1'].skew()
                self.feature_df.loc[row, 'kurt_buy_volume'] = temp['bc1'].kurt()
                
                self.feature_df.loc[row, 'average_sell_volume'] = temp['sc1'].mean()
                self.feature_df.loc[row, 'total_sell_volume'] = temp['sc1'].sum()
                self.feature_df.loc[row, 'max_sell_volume'] = temp['sc1'].max()
                self.feature_df.loc[row, 'min_sell_volume'] = temp['sc1'].min()
                self.feature_df.loc[row, 'price_max_sell_volume'] = temp.loc[temp['sc1'].idxmax(), 'sc1']
                self.feature_df.loc[row, 'price_min_sell_volume'] = temp.loc[temp['sc1'].idxmin(), 'sc1']
                self.feature_df.loc[row, 'std_sell_volume'] = temp['sc1'].std()
                self.feature_df.loc[row, 'skew_sell_volume'] = temp['sc1'].skew()
                self.feature_df.loc[row, 'kurt_sell_volume'] = temp['sc1'].kurt()
                
                up_temp = temp[temp['close']>temp['open']]
                if not up_temp.empty:
                    self.feature_df.loc[row, 'up_average_volume'] = up_temp['vol'].mean()
                    self.feature_df.loc[row, 'up_total_volume'] = up_temp['vol'].sum()
                    self.feature_df.loc[row, 'up_max_volume'] = up_temp['vol'].max()
                    self.feature_df.loc[row, 'up_min_volume'] = up_temp['vol'].min()
                    self.feature_df.loc[row, 'up_price_max_volume'] = up_temp.loc[up_temp['close'].idxmax(), 'vol']
                    self.feature_df.loc[row, 'up_price_min_volume'] = up_temp.loc[up_temp['close'].idxmin(), 'vol']
                    self.feature_df.loc[row, 'up_std_volume'] = up_temp['vol'].std()
                    self.feature_df.loc[row, 'up_skew_volume'] = up_temp['vol'].skew()
                    self.feature_df.loc[row, 'up_kurt_volume'] = up_temp['vol'].kurt()

                    self.feature_df.loc[row, 'up_average_buy_volume'] = up_temp['bc1'].mean()
                    self.feature_df.loc[row, 'up_total_buy_volume'] = up_temp['bc1'].sum()
                    self.feature_df.loc[row, 'up_max_buy_volume'] = up_temp['bc1'].max()
                    self.feature_df.loc[row, 'up_min_buy_volume'] = up_temp['bc1'].min()
                    self.feature_df.loc[row, 'up_price_max_buy_volume'] = up_temp.loc[up_temp['close'].idxmax(), 'bc1']
                    self.feature_df.loc[row, 'up_price_min_buy_volume'] = up_temp.loc[up_temp['close'].idxmin(), 'bc1']
                    self.feature_df.loc[row, 'up_std_buy_volume'] = up_temp['bc1'].std()
                    self.feature_df.loc[row, 'up_skew_buy_volume'] = up_temp['bc1'].skew()
                    self.feature_df.loc[row, 'up_kurt_buy_volume'] = up_temp['bc1'].kurt()

                    self.feature_df.loc[row, 'up_average_sell_volume'] = up_temp['sc1'].mean()
                    self.feature_df.loc[row, 'up_total_sell_volume'] = up_temp['sc1'].sum()
                    self.feature_df.loc[row, 'up_max_sell_volume'] = up_temp['sc1'].max()
                    self.feature_df.loc[row, 'up_min_sell_volume'] = up_temp['sc1'].min()
                    self.feature_df.loc[row, 'up_price_max_sell_volume'] = up_temp.loc[up_temp['close'].idxmax(), 'sc1']
                    self.feature_df.loc[row, 'up_price_min_sell_volume'] = up_temp.loc[up_temp['close'].idxmin(), 'sc1']
                    self.feature_df.loc[row, 'up_std_sell_volume'] = up_temp['sc1'].std()
                    self.feature_df.loc[row, 'up_skew_sell_volume'] = up_temp['sc1'].skew()
                    self.feature_df.loc[row, 'up_kurt_sell_volume'] = up_temp['sc1'].kurt()
                else:
                    self.feature_df.loc[row, ['up_average_volume','up_total_volume', 'up_max_volume', 'up_min_volume', 
                                              'up_price_max_volume', 'up_price_min_volume', 
                                              'up_std_volume', 'up_skew_volume','up_kurt_volume']] = 0
                    self.feature_df.loc[row, ['up_average_buy_volume','up_total_buy_volume', 'up_max_buy_volume', 'up_min_buy_volume', 
                                              'up_price_max_buy_volume', 'up_price_min_buy_volume', 
                                              'up_std_buy_volume', 'up_skew_buy_volume','up_kurt_buy_volume']] = 0
                    self.feature_df.loc[row, ['up_average_sell_volume','up_total_sell_volume', 'up_max_sell_volume', 'up_min_sell_volume', 
                                              'up_price_max_sell_volume', 'up_price_min_sell_volume', 
                                              'up_std_sell_volume', 'up_skew_sell_volume','up_kurt_sell_volume']] = 0
                
                down_temp = temp[temp['close']<temp['open']]
                if not down_temp.empty:
                    self.feature_df.loc[row, 'down_average_volume'] = down_temp['vol'].mean()
                    self.feature_df.loc[row, 'down_total_volume'] = down_temp['vol'].sum()
                    self.feature_df.loc[row, 'down_max_volume'] = down_temp['vol'].max()
                    self.feature_df.loc[row, 'down_min_volume'] = down_temp['vol'].min()
                    self.feature_df.loc[row, 'down_price_max_volume'] = down_temp.loc[down_temp['close'].idxmax(), 'vol']
                    self.feature_df.loc[row, 'down_price_min_volume'] = down_temp.loc[down_temp['close'].idxmin(), 'vol']
                    self.feature_df.loc[row, 'down_std_volume'] = down_temp['vol'].std()
                    self.feature_df.loc[row, 'down_skew_volume'] = down_temp['vol'].skew()
                    self.feature_df.loc[row, 'down_kurt_volume'] = down_temp['vol'].kurt()

                    self.feature_df.loc[row, 'down_average_buy_volume'] = down_temp['bc1'].mean()
                    self.feature_df.loc[row, 'down_total_buy_volume'] = down_temp['bc1'].sum()
                    self.feature_df.loc[row, 'down_max_buy_volume'] = down_temp['bc1'].max()
                    self.feature_df.loc[row, 'down_min_buy_volume'] = down_temp['bc1'].min()
                    self.feature_df.loc[row, 'down_price_max_buy_volume'] = down_temp.loc[down_temp['close'].idxmax(), 'bc1']
                    self.feature_df.loc[row, 'down_price_min_buy_volume'] = down_temp.loc[down_temp['close'].idxmin(), 'bc1']
                    self.feature_df.loc[row, 'down_std_buy_volume'] = down_temp['bc1'].std()
                    self.feature_df.loc[row, 'down_skew_buy_volume'] = down_temp['bc1'].skew()
                    self.feature_df.loc[row, 'down_kurt_buy_volume'] = down_temp['bc1'].kurt()

                    self.feature_df.loc[row, 'down_average_sell_volume'] = down_temp['sc1'].mean()
                    self.feature_df.loc[row, 'down_total_sell_volume'] = down_temp['sc1'].sum()
                    self.feature_df.loc[row, 'down_max_sell_volume'] = down_temp['sc1'].max()
                    self.feature_df.loc[row, 'down_min_sell_volume'] = down_temp['sc1'].min()
                    self.feature_df.loc[row, 'down_price_max_sell_volume'] = down_temp.loc[down_temp['close'].idxmax(), 'sc1']
                    self.feature_df.loc[row, 'down_price_min_sell_volume'] = down_temp.loc[down_temp['close'].idxmin(), 'sc1']
                    self.feature_df.loc[row, 'down_std_sell_volume'] = down_temp['sc1'].std()
                    self.feature_df.loc[row, 'down_skew_sell_volume'] = down_temp['sc1'].skew()
                    self.feature_df.loc[row, 'down_kurt_sell_volume'] = down_temp['sc1'].kurt()
                else:
                    self.feature_df.loc[row, ['down_average_volume','down_total_volume', 'down_max_volume', 'down_min_volume', 
                                          'down_price_max_volume', 'down_price_min_volume', 
                                          'down_std_volume', 'down_skew_volume','down_kurt_volume']] = 0
                    self.feature_df.loc[row, ['down_average_buy_volume','down_total_buy_volume', 'down_max_buy_volume', 'down_min_buy_volume', 
                                          'down_price_max_buy_volume', 'down_price_min_buy_volume', 
                                          'down_std_buy_volume', 'down_skew_buy_volume','down_kurt_buy_volume']] = 0
                    self.feature_df.loc[row, ['down_average_sell_volume','down_total_sell_volume', 'down_max_sell_volume', 'down_min_sell_volume', 
                                          'down_price_max_sell_volume', 'down_price_min_sell_volume', 
                                          'down_std_sell_volume', 'down_skew_sell_volume','down_kurt_sell_volume']] = 0 
                if len(temp)>=5:
                    hist = np.histogram(temp['vol'], bins=5)[0]
                    prob = hist/hist.sum()
                    prob = prob[prob>0]
                    self.feature_df.loc[row, 'binned_entropy'] = -np.sum(prob*np.log(prob))
                else:
                    self.feature_df.loc[row, 'binned_entropy'] = 0

            else:
                self.feature_df.loc[row, factor_name] = 0
        return None

    def factor_return_generation(self):
        '''
        预测未来交易与股票近期的交易不对称有关：（持续补充）
            （1）总收益率因子：回溯区间内总收益率
            （2）平均收益率因子：回溯区间内平均收益率
            （3）最大收益率因子：回溯区间内最大收益率
            （4）最小收益率因子：回溯区间内最小收益率
            （5）价格最高收益率因子：回溯区间内价格最高收益率
            （6）价格最低收益率因子：回溯区间内价格最低收益率
            （7）收益率标准差因子：回溯区间内收益率的标准差
            （8）收益率偏度因子：回溯区间内收益率的偏度
            （9）收益率峰度因子：回溯区间内收益率的峰度

            （10）上行收益率因子：回溯区间内总上行收益率
            （11）上行平均收益率因子：回溯区间内上行tick的平均收益率
            （12）上行最大收益率因子：回溯区间内上行tick的最大收益率
            （13）上行最小收益率因子：回溯区间内上行tick的最小收益率
            （14）上行价格最高收益率因子：回溯区间内上行tick价格最高收益率
            （15）上行价格最低收益率因子：回溯区间内上行tick价格最低收益率
            （16）上行收益率标准差因子：回溯区间内上行tick收益率的标准差
            （17）上行收益率偏度因子：回溯区间内上行tick收益率的偏度
            （18）上行收益率峰度因子：回溯区间内上行tick收益率的峰度

            （19）下行收益率因子：回溯区间内总下行收益率
            （20）下行平均收益率因子：回溯区间内下行tick的平均收益率
            （21）下行最大收益率因子：回溯区间内下行tick的最大收益率
            （22）下行最小收益率因子：回溯区间内下行tick的最小收益率
            （23）下行价格最高收益率因子：回溯区间内下行tick价格最高收益率
            （24）下行价格最低收益率因子：回溯区间内下行tick价格最低收益率
            （25）下行收益率标准差因子：回溯区间内下行tick收益率的标准差
            （26）下行收益率偏度因子：回溯区间内下行tick收益率的偏度
            （27）下行收益率峰度因子：回溯区间内下行tick收益率的峰度

            （28）上行tick数因子：回溯区间内上行tick的个数
            （29）下行tick数因子：回溯区间内下行tick的个数
            （30）持平tick数因子：回溯区间内持平tick的个数
            （31）上行tick数占比因子：回溯区间内上行tick的个数 / 回溯区间内tick的总数
            日期7.24

            （32）乖离率：计算回溯窗口每个tick的平均收盘价，计算回溯区间收盘价相对于平均收盘价的偏差
                BIAS乖离率指标通过测算股价在波动过程中与移动平均线出现偏离的程度，
                从而得出股价在剧烈波动时因偏离移动平均趋势造成的可能回档或反弹
            （33）close/vwap：计算回溯窗口内的vwap，计算回溯区间收盘价相对于vwap的偏差
            （34）时序回归因子：回溯区间内收盘价序列对时间下标做回归，close=a+b*t+c*t^2，t^2刻画了趋势的非线性部分
            时序回归因子=b*R^2
                主次价差因子为次主力资金较主力资金收益率在过去回溯区间的均值，次主力资金较高时做多，次主力资金较低时做空，
                通过收益率差收敛获得收益
            （35）时间加权平均的股票相对价格位置：股票当期价格相对区间最高最低价的分位数
                衡量股票在价格相对高位停留的时间长短，停留时间越长，因子取值越大
            （36）简单波动率：承担股票的波动性风险，也可以获得相应的风险补偿
            （37）日内信噪比：使用经验模态分解EMD算法分解出价格序列中的噪音序列和信号序列，
            计算信号序列的标准差与噪声序列标准差的比值的对数，即为日内信噪比
                日内信噪比因子与股票未来收益正相关，信噪比越大，股票未来收益越高，
                从行为金融学的角度看，价格偏离内在价值越频繁，未来收益越差
            （38）间隔跳空：衡量价格的累积跳空，正值表示普遍高开，负值表示普遍低开
            （39）人气指数：刻画的是市场交易人气，人气越旺，股价越高
            （40）大单影响力：回溯区间内超过分位数90%的大额成交量对应的收益率
                被新进的大资金或者拥有信息优势的老资金持续看好，那大单之后的快照收益平均为正，如果持续看空，平均收益应为负
            （41）上下行波动率不对称性：RSJ衡量了上下行波动的不对称性；RSJ通常有负风险溢价，
                短期日内情绪不稳定的大幅上涨往往跟着未来的补跌，短期日内情绪不稳定的大幅下跌也往往跟着未来的不涨。
            （42）相对强弱：利用日内1分钟收盘价序列计算日内每分钟上涨总幅度占变化总幅度数据的百分比
                RSI指标通过衡量一段时间内股价上涨总幅度占股价变化总幅度平均值的百分比来评估多空力量的强弱程度，
                反映了市场在一定时期内的景气程度
            （43）多层加权日内信噪比:权重对第二层和第三层信噪比因子加权得到，Vol为日内价格波动率，先在横截面上对其做极值处理和归一化处理，
            然后将标准化后的波动率缩至合适的区间，信噪比因子由日内价格波动为权重对第二层和第三层信噪比因子加权得到；
                日内价格波动越大，剥离更多噪声，赋予第三层更大的权重；加权后的复合信噪比的表现有所提升
            （44）价格拐点：拐点的定义为：当最新价格不是持续向上或者向下的，也就是当前最新价变动方向与上一次变动方向不一致时即为一次拐点
        '''
        factor_name = ['total_return','average_return','max_return','min_return',
                        'price_max_return','price_min_return',
                        'std_return','skew_return','kurt_return',
                        'up_total_return','up_average_return','up_max_return','up_min_return',
                        'up_price_max_return','up_price_min_return',
                        'up_std_return','up_skew_return','up_kurt_return',
                        'down_total_return','down_average_return','down_max_return','down_min_return',
                        'down_price_max_return','down_price_min_return',
                        'down_std_return','down_skew_return','down_kurt_return',
                        'up_tick_count','down_tick_count','flat_tick_count',
                        'up_tick_count_ratio',

                        'deviation_rate','close_vwap','time_series_factor','product_channel','RPP',
                        'simple_volatility', 'intraday_snr', 'gap', 'popularity_index',
                        'volatility_asymmetry','RSI', 'inflection_count', ]
        for row in tqdm(self.df.index):
            mask = ((self.df['endtime'] >= self.df.loc[row, 'backward_start_time']) & 
                    (self.df['endtime'] <= self.df.loc[row, 'backward_end_time']))
            temp = self.df.loc[mask, :]
            if not temp.empty:
                first_open = temp['open'].iloc[0]
                last_close = temp['close'].iloc[-1]
                self.feature_df.loc[row, 'total_return'] = last_close/first_open - 1
                self.feature_df.loc[row, 'average_return'] = (temp['close']/temp['open'] - 1).mean()
                self.feature_df.loc[row, 'max_return'] = (temp['close']/temp['open'] - 1).max()
                self.feature_df.loc[row, 'min_return'] = (temp['close']/temp['open'] - 1).min()
                self.feature_df.loc[row, 'price_max_return'] = temp.loc[temp['close'].idxmax(), 'close']/temp.loc[temp['close'].idxmin(), 'open'] - 1
                self.feature_df.loc[row, 'price_min_return'] = temp.loc[temp['close'].idxmin(), 'close']/temp.loc[temp['close'].idxmax(), 'open'] - 1
                self.feature_df.loc[row, 'std_return'] = (temp['close']/temp['open'] - 1).std()
                self.feature_df.loc[row, 'skew_return'] = (temp['close']/temp['open'] - 1).skew()
                self.feature_df.loc[row, 'kurt_return'] = (temp['close']/temp['open'] - 1).kurt()

                up_temp = temp[temp['close']>temp['open']]
                if not up_temp.empty:
                    self.feature_df.loc[row, 'up_total_return'] = (up_temp['close']/up_temp['open'] - 1).sum()
                    self.feature_df.loc[row, 'up_average_return'] = (up_temp['close']/up_temp['open'] - 1).mean()
                    self.feature_df.loc[row, 'up_max_return'] = (up_temp['close']/up_temp['open'] - 1).max()
                    self.feature_df.loc[row, 'up_min_return'] = (up_temp['close']/up_temp['open'] - 1).min()
                    self.feature_df.loc[row, 'up_price_max_return'] = up_temp.loc[up_temp['close'].idxmax(), 'close']/up_temp.loc[up_temp['close'].idxmin(), 'open'] - 1
                    self.feature_df.loc[row, 'up_price_min_return'] = up_temp.loc[up_temp['close'].idxmin(), 'close']/up_temp.loc[up_temp['close'].idxmax(), 'open'] - 1
                    self.feature_df.loc[row, 'up_std_return'] = (up_temp['close']/up_temp['open'] - 1).std()
                    self.feature_df.loc[row, 'up_skew_return'] = (up_temp['close']/up_temp['open'] - 1).skew()
                    self.feature_df.loc[row, 'up_kurt_return'] = (up_temp['close']/up_temp['open'] - 1).kurt()
                else:
                    self.feature_df.loc[row, ['up_total_return','up_average_return','up_max_return','up_min_return',
                                              'up_price_max_return','up_price_min_return',
                                              'up_std_return','up_skew_return','up_kurt_return']] = 0
                
                down_temp = temp[temp['close']<temp['open']]
                if not down_temp.empty:
                    self.feature_df.loc[row, 'down_total_return'] = (down_temp['close']/down_temp['open'] - 1).sum()
                    self.feature_df.loc[row, 'down_average_return'] = (down_temp['close']/down_temp['open'] - 1).mean()
                    self.feature_df.loc[row, 'down_max_return'] = (down_temp['close']/down_temp['open'] - 1).max()
                    self.feature_df.loc[row, 'down_min_return'] = (down_temp['close']/down_temp['open'] - 1).min()
                    self.feature_df.loc[row, 'down_price_max_return'] = down_temp.loc[down_temp['close'].idxmax(), 'close']/down_temp.loc[down_temp['close'].idxmin(), 'open'] - 1
                    self.feature_df.loc[row, 'down_price_min_return'] = down_temp.loc[down_temp['close'].idxmin(), 'close']/down_temp.loc[down_temp['close'].idxmax(), 'open'] - 1
                    self.feature_df.loc[row, 'down_std_return'] = (down_temp['close']/down_temp['open'] - 1).std()
                    self.feature_df.loc[row, 'down_skew_return'] = (down_temp['close']/down_temp['open'] - 1).skew()
                    self.feature_df.loc[row, 'down_kurt_return'] = (down_temp['close']/down_temp['open'] - 1).kurt()
                else:
                    self.feature_df.loc[row, ['down_total_return','down_average_return','down_max_return','down_min_return',
                                              'down_price_max_return','down_price_min_return',
                                              'down_std_return','down_skew_return','down_kurt_return']] = 0
                
                self.feature_df.loc[row, 'up_tick_count'] = up_temp.shape[0]
                self.feature_df.loc[row, 'down_tick_count'] = down_temp.shape[0]
                self.feature_df.loc[row, 'flat_tick_count'] = temp.shape[0] - up_temp.shape[0] - down_temp.shape[0]
                self.feature_df.loc[row, 'up_tick_count_ratio'] = up_temp.shape[0]/temp.shape[0]

                self.feature_df.loc[row, 'deviation_rate'] = temp['close'].iloc[-1] / temp['close'].mean() - 1
                self.feature_df.loc[row, 'close_vwap'] = temp['close'].iloc[-1] / ((temp['close']*temp['volume']).sum() / temp['volume'].sum())
                
                closes = temp['close'].values
                times = np.arange(len(closes))
                X = np.column_stack([times, times**2]) # 添加趋势项
                model = LinearRegression()
                model.fit(X, closes)
                b_coef = model.coef_[0]
                r_squared = model.score(X, closes)
                self.feature_df.loc[row, 'time_series_factor'] = b_coef * r_squared

                type_price = (temp['close'] + temp['high'] + temp['low'])/3
                type_price_mean = type_price.mean()
                average_dev = (abs(type_price - type_price_mean)).mean()
                self.feature_df.loc[row, 'product_channel'] = (type_price.iloc[-1] - type_price_mean) / (0.015*average_dev)

                temp['time_diff'] = temp['timestamp'].diff().dt.total_seconds().fillna(0)
                self.feature_df.loc[row, 'RPP'] = (((temp['close'] * temp['time_diff']).sum() / temp['time_diff'].sum()) - temp['low'].min()) / (temp['high'].max() - temp['low'].min())
                
                time_span = (temp['timestamp'].iloc[-1] - temp['timestamp'].iloc[0]).total_seconds() / 3600
                adjustment_factor = 24 / time_span if time_span > 0 else 1
                temp['ret'] = temp['close'] / temp['open'] - 1
                ret_mean = temp['ret'].mean()
                self.feature_df.loc[row, 'simple_volatility'] = np.sqrt(adjustment_factor) * ((temp['ret'] - ret_mean) ** 2).sum() / temp.shape[0]
                
                if len(temp) >= 10:
                    imfs = EMD()(temp['close'].values)
                    if len(imfs) > 1:
                        signal = np.sum(imfs[:-1], axis=0)
                        noise = imfs[-1]
                        ratio = np.std(signal) / (np.std(noise) + 1e-6)
                        self.feature_df.loc[row, 'intraday_snr'] = np.log(ratio)
                    else:
                        self.feature_df.loc[row, 'intraday_snr'] = 0
                else:
                    self.feature_df.loc[row, 'intraday_snr'] = 0
                
                log_gap = np.log(temp['open'] / temp['close'].shift(1)).dropna()
                self.feature_df.loc[row, 'gap'] = log_gap.sum() if not log_gap.empty else 0
                self.feature_df.loc[row, 'popularity_index'] = 100 * (temp['high'] - temp['open']).sum() / (temp['open'] - temp['low']).sum()
                large_order_threshold = temp['volume'].quantile(0.9)
                large_orders = temp[temp['volume'] >= large_order_threshold]
                if not large_orders.empty:
                    self.feature_df.loc[row, 'influence_large_orders'] = (large_orders['close'] / large_orders['open'] - 1).mean()
                else:
                    self.feature_df.loc[row, 'influence_large_orders'] = 0
                
                up = temp[temp['close'] > temp['open']]
                down = temp[temp['close'] < temp['open']]
                if not up.empty:
                    up_d = ((up['close'] / up['open'] - 1) ** 2).sum()
                else:
                    up_d = 0
                if not down.empty:
                    down_d = ((down['close'] / down['open'] - 1) ** 2).sum()
                else:
                    down_d = 0
                self.feature_df.loc[row, 'volatility_asymmetry'] = (up_d - down_d) / ((temp['close'] / temp['open'] - 1) ** 2).sum()

                up_sma = (temp['close'] - temp['close'].shift(1)).fillna(0).mean()
                self.feature_df.loc[row, 'RSI'] = up_sma / abs(temp['close'] - temp['close'].shift(0)).fillna(0).mean()

                std = temp['close'].std()
                weight = 0.5 + (std-0.5) / 5
                if len(temp) >= 10:
                    imfs_layer1 = EMD()(temp['close'].values)
                    if len(imfs_layer1) >= 10:
                        noise_layer1 = imfs_layer1[-1]
                        imfs_layer2 = EMD()(noise_layer1)
                        if len(imfs_layer2) > 1:
                            noise_layer2 = imfs_layer2[-1]
                            signal_layer2 = np.sum(imfs_layer2[:-1], axis=0)
                            snr_layer2 = np.log(np.std(signal_layer2) / (np.std(noise_layer2) + 1e-6))
                            self.feature_df.loc[row, 'new_snr'] = weight * snr_layer2
                            if len(imfs_layer2) >= 10:
                                imfs_layer3 = EMD()(noise_layer2)
                                if len(imfs_layer3) > 1:
                                    noise_layer3 = imfs_layer3[-1]
                                    signal_layer3 = np.sum(imfs_layer3[:-1], axis=0)
                                    snr_layer3 = np.log(np.std(signal_layer3) / (np.std(noise_layer3) + 1e-6))
                                    self.feature_df.loc[row, 'new_snr'] = weight * snr_layer2 + (1 - weight) * snr_layer3 
                        else:
                            self.feature_df.loc[row, 'new_snr'] = 0                    
                    else:
                        self.feature_df.loc[row, 'new_snr'] = 0
                else:
                    self.feature_df.loc[row, 'new_snr'] = 0

                if not len(temp) < 3:
                    diffs = np.diff(temp['close'])  # 计算差分
                    directions = np.sign(diffs)  # 获取方向（1:涨, -1:跌, 0:平）
                    directions = directions[directions != 0]
                    inflection_count = 0
                    for i in range(1, len(directions)):
                        if directions[i] != directions[i-1]:
                            inflection_count += 1
                    self.feature_df.loc[row, 'inflection_count'] = inflection_count
                else:
                    self.feature_df.loc[row, 'inflection_count'] = 0
            else:
                self.feature_df.loc[row, factor_name] = 0
        return None
    
    def factor_price_volume_relation_generation(self):
        '''
        预测未来交易与量价关系有关（持续补充）：
            （1）Amihud非流动性比例：收益率绝对值除以成交额在回溯区间上的均值
                因子取值越高，股票的流动性越差；流动性更大的时候买入可以获得非流动性补偿
            （2）b型成交量分布：计算同价成交量，将成交量累计最大的价格定义为该股当个回溯区间的成交量支撑点（VSP），
            逐步计算VSP周围的成交量累计值（价格由近及远，成交量由大及小），该累计值与全天成交量总和的比值超50%的最小区域，
            即为成交量支撑区域VSA，价格上限为VSA_high，下限价格为VSA_low，VSA_high - VSA_loe
                若上限价格与当日最低价接近，说明成交量支撑区域越接近近日内的低价区域，则该股日内的成交量分布越接近于b型成交量分布，预期上涨
            （3）非流动性：不考虑日内价格变动方向，直接进行无向叠加，求得股价变动的最短路径，计算每日的K线最短路径非流动性因子
                通过使用更高频的K线数据提升K线最短路径对股票交易时市场冲击的代理精度
        '''
        factor_name = ['Amihud_illiquidity_ratio','Type_B_volume_distribution','illiquidity']
        for row in tqdm(self.df.index):
            mask = ((self.df['endtime'] >= self.df.loc[row, 'backward_start_time']) & 
                    (self.df['endtime'] <= self.df.loc[row, 'backward_end_time']))
            temp = self.df.loc[mask, :]
            if not temp.empty:
                self.feature_df.loc[row, 'Amihud_illiquidity_ratio'] = (abs(temp['close'] / temp['open'] - 1)  / temp['amount']).sum() / temp.shape[0]
                
                price_volume = temp.groupby('price')['amount'].sum().sort_values(ascending=False)
                vsp_price = price_volume.idxmax()  # 成交量最大的价格(VSP)
                total_volume = price_volume.sum() 
                cumulative_ratio = 0
                vsa_prices = []
                # 从VSP向两侧扩展，累计成交量直至>50%
                for price in sorted(price_volume.index, key=lambda x: abs(x - vsp_price)):
                    cumulative_ratio += price_volume[price] / total_volume
                    vsa_prices.append(price) # 计算VSA
                    if cumulative_ratio > 0.5:
                        break
                vsa_high = max(vsa_prices)
                vsa_low = min(vsa_prices)
                self.feature_df.loc[row, 'Type_B_volume_distribution'] = vsa_high - vsa_low

                shortcut = 2*(temp['high'] - temp['low']) - abs(temp['close'] - temp['open'])
                self.feature_df.loc[row, 'illiquidity'] = (shortcut/temp['amount']).sum()

            else:
                self.feature_df.loc[row, factor_name] = 0
        return None
    
    def factor_imbalance_generation(self):
        '''
        预测未来交易与订单买卖不平衡度有关（持续补充）：
            （1）市价偏离度：平均交易价格和委托挂单价格的差值
                当市价偏离度为正，交易均价更接近卖一价，为卖方发起的交易，卖压大，未来价格更趋向于下行
            （2）市价偏离度_vwap：vwap和委托挂单价格的差值
            （3）买入意愿：回溯区间内所有买一量大于卖一量tick的数量除以回溯区间内所有tick数量
            （4）

            
            
        '''
        factor_name = ['market_deviation', 'market_deviation_vwap', 'willingness_to_buy', ]
        for row in tqdm(self.df.index):
            mask = ((self.df['endtime'] >= self.df.loc[row, 'backward_start_time']) & 
                    (self.df['endtime'] <= self.df.loc[row, 'backward_end_time']))
            temp = self.df.loc[mask, :]
            if not temp.empty:
                self.feature_df.loc[row, 'market_deviation'] = temp['close'].mean() - (temp['buy1'].iloc[-1] + temp['sale1'].iloc[-1])/2
                self.feature_df.loc[row, 'market_deviation_vwap'] = (temp['close']*temp['volume']).sum() / temp['volume'].sum() - (temp['buy1'].iloc[-1] + temp['sale1'].iloc[-1])/2
                self.feature_df.loc[row, 'willingness_to_buy'] = temp[temp['bc1'] > temp['sc1']].shape[0] / temp.shape[0]
                


            else:
                self.feature_df.loc[row, factor_name] = 0
        return None

class automation_feature_():
    def __init__(self, data, kwargs):
        self.df = data
        self.df['endtime'] = pd.to_datetime(self.df['endtime'])
        self.backward_start_minute = kwargs['backward_start_minute']
        self.backward_end_minute = kwargs['backward_end_minute']
        self.predict_type = kwargs['predict_type']
        self.forward_start_minute = kwargs['forward_start_minute']
        self.forward_end_minute = kwargs['forward_end_minute']
        self.feature_df = pd.DataFrame()
        self.feature_df['endtime'] = self.df['endtime']
    
    def _time_interval(self):
        """
        处理回溯区间和预测区间的时间戳：
           回溯时间开始时间：current_time - backward_start_minute
           回溯时间结束时间：current_time - backward_end_minute
           出现异常：早盘开盘前（< 09:25:00）
                    午盘开盘前（< 13:00:00 &> 11:30:00）

           预测时间开始时间：current_time + forward_start_minute
           预测时间结束时间：current_time + forward_end_minute
           出现异常：早盘休市后（> 11:30:00 &< 13:00:00）
                    午盘休市后（> 15:00:00）
        """
        #回溯区间
        self.df['backward_start_time'] = self.df['endtime'] - pd.to_timedelta(self.backward_start_minute, unit='min')
        self.df['backward_end_time'] = self.df['endtime'] - pd.to_timedelta(self.backward_end_minute, unit='min')
        
        # 处理早盘开盘前的情况 (< 09:25:00)
        before_morning_mask = (self.df['backward_start_time'].dt.time < pd.to_datetime('09:25:00').time())
        prev_day = self.df.loc[before_morning_mask, 'backward_start_time'].dt.normalize() - BDay(1)
        current_time = self.df.loc[before_morning_mask, 'backward_start_time'].dt.time
        correct_morning_time = pd.to_timedelta('15:00:00') - (pd.to_timedelta('09:25:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_morning_mask, 'backward_start_time'] = prev_day + correct_morning_time
    
        before_morning_mask = (self.df['backward_end_time'].dt.time < pd.to_datetime('09:25:00').time())
        prev_day = self.df.loc[before_morning_mask, 'backward_end_time'].dt.normalize() - BDay(1)
        current_time = self.df.loc[before_morning_mask, 'backward_end_time'].dt.time
        correct_morning_time = pd.to_timedelta('15:00:00') - (pd.to_timedelta('09:25:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_morning_mask, 'backward_end_time'] = prev_day + correct_morning_time

        # 处理午盘开盘前的情况 (< 13:00:00 &> 11:30:00)
        before_afternoon_mask = ((self.df['backward_start_time'].dt.time < pd.to_datetime('13:00:00').time()) & 
                          (self.df['backward_start_time'].dt.time > pd.to_datetime('11:30:00').time()))
        current_time = self.df.loc[before_afternoon_mask, 'backward_start_time'].dt.time
        correct_afternoon_time = pd.to_timedelta('11:30:00') - (pd.to_timedelta('13:00:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_afternoon_mask, 'backward_start_time'] = self.df.loc[before_afternoon_mask, 'backward_start_time'].dt.normalize() + correct_afternoon_time
        
        before_afternoon_mask = ((self.df['backward_end_time'].dt.time < pd.to_datetime('13:00:00').time()) & 
                          (self.df['backward_end_time'].dt.time > pd.to_datetime('11:30:00').time()))
        current_time = self.df.loc[before_afternoon_mask, 'backward_end_time'].dt.time
        correct_afternoon_time = pd.to_timedelta('11:30:00') - (pd.to_timedelta('13:00:00') - pd.to_timedelta(current_time.astype(str)))
        self.df.loc[before_afternoon_mask, 'backward_end_time'] = self.df.loc[before_afternoon_mask, 'backward_end_time'].dt.normalize() + correct_afternoon_time
        
        if not self.predict_type == 'five_class':
            #预测区间
            self.df['forward_start_time'] = self.df['endtime'] + pd.to_timedelta(self.forward_start_minute, unit='min')
            self.df['forward_end_time'] = self.df['endtime'] + pd.to_timedelta(self.forward_end_minute, unit='min')

            # 处理早盘休市后的情况 (> 11:30:00 &< 13:00:00)
            after_morning_mask = ((self.df['forward_start_time'].dt.time > pd.to_datetime('11:30:00').time()) & 
                              (self.df['forward_start_time'].dt.time < pd.to_datetime('13:00:00').time()))
            current_time = self.df['forward_start_time'].dt.time
            correct_morning_time = pd.to_timedelta('13:00:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                  pd.to_timedelta('11:30:00'))
            self.df.loc[after_morning_mask, 'forward_start_time'] = self.df.loc[after_morning_mask, 'forward_start_time'].dt.normalize() + correct_morning_time

            after_morning_mask = ((self.df['forward_end_time'].dt.time > pd.to_datetime('11:30:00').time()) & 
                              (self.df['forward_end_time'].dt.time < pd.to_datetime('13:00:00').time()))
            current_time = self.df['forward_end_time'].dt.time
            correct_morning_time = pd.to_timedelta('13:00:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                  pd.to_timedelta('11:30:00'))
            self.df.loc[after_morning_mask, 'forward_end_time'] = self.df.loc[after_morning_mask, 'forward_end_time'].dt.normalize() + correct_morning_time 

            # 处理午盘休市后的情况 (> 15:00:00)
            after_afternoon_mask = (self.df['forward_start_time'].dt.time > pd.to_datetime('15:00:00').time())
            next_day = self.df.loc[after_afternoon_mask, 'forward_start_time'].dt.normalize() + BDay(1)
            current_time = self.df.loc[after_afternoon_mask, 'forward_start_time'].dt.time
            correct_afternoon_time = pd.to_timedelta('09:25:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                    pd.to_timedelta('15:00:00'))
            self.df.loc[after_afternoon_mask, 'forward_start_time'] = next_day + correct_afternoon_time

            after_afternoon_mask = (self.df['forward_end_time'].dt.time > pd.to_datetime('15:00:00').time())
            next_day = self.df.loc[after_afternoon_mask, 'forward_end_time'].dt.normalize() + BDay(1)
            current_time = self.df.loc[after_afternoon_mask, 'forward_end_time'].dt.time
            correct_afternoon_time = pd.to_timedelta('09:25:00') + (pd.to_timedelta(current_time.astype(str)) - 
                                                                    pd.to_timedelta('15:00:00'))
            self.df.loc[after_afternoon_mask, 'forward_end_time'] = next_day + correct_afternoon_time
        return None
        '''
        因子自动化框架：
            1.筛选算子+构建算子
            2.生成名称并计算
        '''
    def filter_all_single1(self,df1,filter_name):
        df_mark = pd.Series()
        if filter_name == "timeseries_up":
            df_mark = self.timeseries_up(df1)
        elif filter_name == "timeseries_down":
            df_mark = self.timeseries_down(df1)
        elif filter_name == "timeseries_diff_rank_up":
            df_mark = self.timeseries_diff_rank_up(df1)
        elif filter_name == "timeseries_diff_rank_down":
            df_mark = self.timeseries_diff_rank_down(df1)
        elif filter_name == "rolling_up_up":
            df_mark = self.rolling_up_up(df1)
        elif filter_name == "rolling_up_down":
            df_mark = self.rolling_up_down(df1)
        elif filter_name == "rolling_down_up":
            df_mark = self.rolling_down_up(df1)
        elif filter_name == "rolling_down_down":
            df_mark = self.rolling_down_down(df1)
        return df_mark
    
    def filter_all_single2(self,df1,filter_name):
        df_mark = pd.Series()
        if filter_name == "timeseries_ratio_up":
            df_mark = self.timeseries_ratio_up(df1)
        elif filter_name == "timeseries_ratio_down":
            df_mark = self.timeseries_ratio_down(df1)
        elif filter_name == "timeseries_diff_rank_up":
            df_mark = self.timeseries_diff_rank_up(df1)
        elif filter_name == "timeseries_diff_rank_down":
            df_mark = self.timeseries_diff_rank_down(df1)
        elif filter_name == "rolling_up_up":
            df_mark = self.rolling_up_up(df1)
        elif filter_name == "rolling_up_down":
            df_mark = self.rolling_up_down(df1)
        elif filter_name == "rolling_down_up":
            df_mark = self.rolling_down_up(df1)
        elif filter_name == "rolling_down_down":
            df_mark = self.rolling_down_down(df1)
        return df_mark
    
    def filter_all_double1(self,df1, df2, filter_name):
        df_mark = pd.DataFrame()
        if filter_name == "timeseries_double_up":
            df_mark = self.timeseries_double_up(df1, df2)
        elif filter_name == "timeseries_double_down":
            df_mark = self.timeseries_double_down(df1, df2)
        return df_mark

    def filter_all_double2(self,df1, df2, filter_name):
        df_mark = pd.DataFrame()
        if filter_name == "timeseries_double_ratio_up":
            df_mark = self.timeseries_double_ratio_up(df1, df2)
        elif filter_name == "timeseries_double_ratio_down":
            df_mark = self.timeseries_double_ratio_down(df1, df2)
        elif filter_name == "timeseries_double_changes_up":
            df_mark = self.timeseries_double_changes_up(df1, df2)
        elif filter_name == "timeseries_double_changes_down":
            df_mark = self.timeseries_double_changes_down(df1, df2)
        elif filter_name == "timeseries_double_rank_up":
            df_mark = self.timeseries_double_rank_up(df1, df2)
        elif filter_name == "timeseries_double_rank_down":
            df_mark = self.timeseries_double_rank_down(df1, df2)
        return df_mark
    
    #单变量筛选
    def timeseries_ratio_up(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        row_sum = np.nansum(df)
        df_ratio = df / np.maximum(row_sum, 1)
        df_ratio_quantile = np.percentile(df_ratio, q=80, axis=0)
        return pd.Series(np.where(df_ratio>df_ratio_quantile, 1, np.nan), index=df_series.index)

    def timeseries_ratio_down(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        row_sum = np.nansum(df)
        df_ratio = df / np.maximum(row_sum, 1)
        df_ratio_quantile = np.percentile(df_ratio, q=20, axis=0)
        return pd.Series(np.where(df_ratio<df_ratio_quantile, 1, np.nan), index=df_series.index)

    def timeseries_up(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        df_quantile = np.percentile(df, q=80, axis=0)
        return pd.Series(np.where(df>df_quantile, 1, np.nan), index=df_series.index)

    def timeseries_down(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        df_quantile = np.percentile(df, q=20, axis=0)
        return pd.Series(np.where(df<df_quantile, 1, np.nan), index=df_series.index)

    def timeseries_diff_rank_up(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        df_rank = rankdata(df, axis=0)
        df_diff_rank = rankdata(np.diff(df, axis=0), axis=0)
        df_diff_rank = np.concatenate([0, df_diff_rank], axis=0)
        return pd.Series(np.where(df_rank>df_diff_rank, 1, np.nan), index=df_series.index)

    def timeseries_diff_rank_down(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        df_rank = rankdata(df, axis=0)
        df_diff_rank = rankdata(np.diff(df, axis=0), axis=0)
        df_diff_rank = np.concatenate([0, df_diff_rank], axis=0)
        return pd.Series(np.where(df_rank < df_diff_rank, 1, np.nan), index=df_series.index)

    def rolling_up_up(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        result = np.zeros_like(df)
        # 连续上涨
        result = np.logical_and(df[2:] > df[1:-1], df[1:-1] > df[:-2])#逻辑与操作
        return pd.Series(np.where(result, 1, np.nan), index=df_series.index)

    def rolling_up_down(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        result = np.zeros_like(df)
        # 由跌转涨
        result = np.logical_and(df[2:] > df[1:-1], df[1:-1] <= df[:-2])
        return pd.Series(np.where(result, 1, np.nan), index=df_series.index)

    def rolling_down_up(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        result = np.zeros_like(df)
        # 由涨转跌
        result = np.logical_and(df[2:] < df[1:-1], df[1:-1] >= df[:-2])
        return pd.Series(np.where(result,1,np.nan), index=df_series.index)

    def rolling_down_down(self,df):
        df_series = df.copy()
        df = df.to_numpy()
        df[np.isnan(df)] = 0
        result = np.zeros_like(df)
        # 连续下跌
        result[2:] = np.logical_and(df[2:] < df[1:-1], df[1:-1] < df[:-2])
        return pd.Series(np.where(result,1,np.nan), index=df_series.index)

    #双变量筛选
    def timeseries_double_up(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        return pd.Series(np.where(df1>df2, 1, np.nan), index=df_series.index)

    def timeseries_double_down(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        return pd.Series(np.where(df1<df2, 1, np.nan), index=df_series.index)

    def timeseries_double_ratio_up(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        row_sum1 = np.nansum(df1, axis=1)
        row_sum2 = np.nansum(df2, axis=1)
        df_ratio1 = df1/np.maximum(row_sum1, 1)
        df_ratio2 = df2/np.maximum(row_sum2, 1)
        return pd.Series(np.where(df_ratio1>df_ratio2, 1, np.nan), index=df_series.index)

    def timeseries_double_ratio_down(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        row_sum1 = np.nansum(df1, axis=1)
        row_sum2 = np.nansum(df2, axis=1)
        df_ratio1 = df1/np.maximum(row_sum1, 1)
        df_ratio2 = df2/np.maximum(row_sum2, 1)
        return pd.Series(np.where(df_ratio1<df_ratio2, 1, np.nan), index=df_series.index)

    def timeseries_double_changes_up(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        df_changes1 = abs(np.diff(df1))/np.maximum(df1[0:(df1.shape[1]-1)], 1e-3)
        df_changes1 = np.concatenate([0, df_changes1], axis=0)
        df_changes2 = abs(np.diff(df2))/np.maximum(df2[0:(df2.shape[1]-1)], 1e-3)
        df_changes2 = np.concatenate([0, df_changes2], axis=0)
        return pd.Series(np.where(df_changes1>df_changes2, 1, np.nan), index=df_series.index)

    def timeseries_double_changes_down(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        df_changes1 = abs(np.diff(df1))/np.maximum(df1[:, 0:(df1.shape[1]-1)], 1e-3)
        df_changes1 = np.concatenate([0, df_changes1], axis=0)
        df_changes2 = abs(np.diff(df2))/np.maximum(df2[:, 0:(df2.shape[1]-1)], 1e-3)
        df_changes2 = np.concatenate([0, df_changes2], axis=0)
        return pd.Series(np.where(df_changes1<df_changes2, 1, np.nan), index=df_series.index)

    def timeseries_double_rank_up(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        df_rank1 = rankdata(df1, axis=0)
        df_rank2 = rankdata(df2, axis=0)
        return pd.Series(np.where(df_rank1>df_rank2, 1, np.nan), index=df_series.index)

    def timeseries_double_rank_down(self,df1, df2):
        df_series = df1.copy()
        df1 = df1.to_numpy()
        df2 = df2.to_numpy()
        df1[np.isnan(df1)] = 0
        df2[np.isnan(df2)] = 0
        df_rank1 = rankdata(df1, axis=0)
        df_rank2 = rankdata(df2, axis=0)
        return pd.Series(np.where(df_rank1<df_rank2, 1, np.nan), index=df_series.index)

    def cal_count(self,df_mark):
        return np.nansum(df_mark, axis=0)

    def cal_closedev_sum(self,df, df_mark, is_filter):
        if is_filter:
            df = df.to_numpy()
            df_mark = df_mark.to_numpy()
            last_close = df[-1]
            return np.nansum(df * df_mark / np.maximum(last_close, 1e-3), axis=0)
        else:
            df = df.to_numpy()
            last_close = df[-1]
            return np.nansum(df / np.maximum(last_close, 1e-3), axis=0)

    def cal_var(self,df, df_mark, is_filter):
        if is_filter:
            df = df.to_numpy()
            df_mark = df_mark.to_numpy()
            df_changes = np.diff(df) / np.maximum(df[0:(df.shape[1]-1)], 1e-2)
            return np.nanvar(df_changes * df_mark[1:], axis=0)
        else:
            df = df.to_numpy()
            df_changes = np.diff(df) / np.maximum(df[0:(df.shape[1]-1)], 1e-2)
            return np.nanvar(df_changes, axis=0)

    def cal_skew(self,df, df_mark, is_filter):
        if is_filter:
            df=df.to_numpy()
            df_mark = df_mark.to_numpy()
            df_changes = np.diff(df) / np.maximum(df[0:(df.shape[1]-1)], 1e-2)
            return skew(df_changes*df_mark[:, 1:], axis=0)
        else:
            df = df.to_numpy()
            df_changes = np.diff(df) / np.maximum(df[0:(df.shape[1]-1)], 1e-2)
            return skew(df_changes, axis=0)

    def cal_ratio(self,df_1,df_2, df_mark, is_filter):
        if is_filter:
            df_1 = df_1.to_numpy()
            df_2 = df_2.to_numpy()
            df_mark = df_mark.to_numpy()
            return np.nansum(df_1 * df_mark, axis=0)/np.maximum(np.nansum(df_2 * df_mark, axis=0), 1)
        else:
            df_1 = df_1.to_numpy()
            df_2 = df_2.to_numpy()
            return np.nansum(df_1, axis=0)/np.maximum(np.nansum(df_2, axis=0), 1)

    def cal_percount_ratio(self,df_1, df_2, df_mark, is_filter):
        if is_filter:
            df_1 = df_1.to_numpy()
            df_2 = df_2.to_numpy()
            df_mark = df_mark.to_numpy()
            df_percount_all = np.nansum(df_1, axis=0)/np.maximum(np.nansum(df_2, axis=0), 1e-1)
            df_percount = np.nansum(df_1*df_mark, axis=0)/np.maximum(np.nansum(df_2*df_mark, axis=0), 1e-1)
            return df_percount / np.maximum(df_percount_all, 1)
        else:
            df_1 = df_1.to_numpy()
            return np.zeros_like(df_1)

    def cal_strength(self,df, df_mark, is_filter):
        if is_filter:
            df = df.to_numpy()
            df[np.isnan(df)]=0
            df_mark = df_mark.to_numpy()
            return np.nanmean(df*df_mark, axis=0)/np.maximum(np.nanstd(df*df_mark, axis=0), 1e-3)
        else:
            df = df.to_numpy()
            df[np.isnan(df)] = 0
            return np.nanmean(df, axis=0)/np.maximum(np.nanstd(df, axis=0), 1e-3)

    def cal_mediandev(self,df, df_mark, is_filter):
        if is_filter:
            df = df.to_numpy()
            df[np.isnan(df)] = 0
            df_median = np.nanmedian(df, axis=0)
            df_dev = df/np.maximum(df_median, 1)
            df_mark = df_mark.to_numpy()
            return np.nansum(df_dev*df_mark, axis=0)
        else:
            df = df.to_numpy()
            df[np.isnan(df)] = 0
            df_median = np.nanmedian(df, axis=0)
            df_dev = df / np.maximum(df_median, 1)
            return np.nansum(df_dev, axis=0)

    def filter_and_cal(self, input_data, prm_config):
        filter_data_number = prm_config["prm"]["filter_data_number"]
        data_dict = {}
        for i in range(0, filter_data_number):
            column = prm_config["prm"]["filter_data"+str(i)]
            data_dict[i] = input_data[column]
        
        filter_way = prm_config["prm"]["filter_way"]
        filter_choice = 0
        #计算筛选算子的输入变量
        if not filter_data_number==0:
            df_filter_numbers = 1
        else:
            df_filter_numbers = 0
        if filter_way == "ret": # 收益率
            df1_1 = data_dict[1]/np.maximum(data_dict[0], 1e-3) - 1
            filter_choice = 1
        elif filter_way == "ret_zeros": # 收益率大于或小于零
            df1_1 = data_dict[1]/np.maximum(data_dict[0], 1e-3) - 1
            df1_2 = pd.Series(np.zeros_like(df1_1), index = df1_1.index)
            df_filter_numbers = 2
            filter_choice = 2
        elif filter_way == "closedev": # 价格偏离度
            if filter_data_number == 2:
                df1_1 = data_dict[1]/np.maximum(data_dict[0], 1e-1)
            elif filter_data_number == 1:
                df1_1 = data_dict[0]
            last = df1_1.iloc[-1]
            df1_1 = df1_1 / np.maximum(last, 1e-3)
            filter_choice = 1
        elif filter_way == "amplitude": # 振幅
            df1_1 = data_dict[1]/np.maximum(data_dict[0], 1e-3)
            filter_choice = 1
        elif filter_way == "gap1": # 跳空
            df1_1 = data_dict[1].to_numpy()
            df1_1 = np.roll(df1_1, 1, axis=0)
            df1_1 = pd.Series(df1_1, index=data_dict[0].index)
            df1_2 = data_dict[0]
            df_filter_numbers = 2
            filter_choice = 2
        elif filter_way == "gap2": # 跳空比率
            df1_1 = data_dict[1].to_numpy()
            df1_1 = np.roll(df1_1, 1, axis=0)
            df1_1 = pd.Series(df1_1, index=data_dict[0].index)
            df1_1 = data_dict[0]/np.maximum(df1_1, 1e-3)
            filter_choice = 1
        elif filter_way == "high_sub_buy": # 少量成交，大量上涨
            df1_1 = (data_dict[1] - data_dict[0])/np.maximum(data_dict[2], 1e-1)
            filter_choice = 1
        elif filter_way == "price_sub_buy":
            df1_1 = abs(data_dict[1] - data_dict[0])/np.maximum(data_dict[2], 1e-1)
            filter_choice = 1
        elif filter_way == "low_sub_sell": # 少量成交，大量下跌
            df1_1 = (data_dict[0] - data_dict[1])/np.maximum(data_dict[2], 1e-1)
            filter_choice = 1
        elif filter_way == "price_sub_sell":
            df1_1 = abs(data_dict[0] - data_dict[1])/np.maximum(data_dict[2], 1e-1)
            filter_choice = 1
        elif filter_way == "price_spreads": # 两个价差比较
            df1_1 = abs(data_dict[1] - data_dict[0])
            df1_2 = abs(data_dict[3] - data_dict[2])
            df_filter_numbers = 2
            filter_choice = 2
        elif filter_way == "double_percount":
            df1_1 = data_dict[1]/np.maximum(data_dict[0], 1e-1)
            df1_2 = data_dict[3]/np.maximum(data_dict[2], 1e-1)
            df_filter_numbers = 2
            filter_choice = 2
        elif filter_way == "self1":
            df1_1 = data_dict[0]
        elif filter_way == "self2":
            df1_1 = data_dict[0]
            df1_2 = data_dict[1]
            df_filter_numbers = 2
        
        filter_name = prm_config["prm"]["filter_name"]
        df_mark = pd.Series()
        if df_filter_numbers == 0:
            is_filter = False
        elif (df_filter_numbers == 1) and (filter_choice == 1):
            is_filter = True
            df_mark = self.filter_all_single1(df1_1, filter_name)
        elif df_filter_numbers == 1:
            is_filter = True
            df_mark = self.filter_all_single2(df1_1, filter_name)
        elif (df_filter_numbers == 2) and (filter_choice == 2):
            is_filter = True
            df_mark = self.filter_all_double1(df1_1, df1_2, filter_name)
        elif df_filter_numbers == 2:
            is_filter = True
            df_mark = self.filter_all_double2(df1_1, df1_2, filter_name)

        cal_data_number = prm_config["prm"]["cal_data_number"]
        data_dict2 = {}
        for i in range(0, cal_data_number):
            column = prm_config["prm"]["cal_data" + str(i)]
            data_dict2[i] = input_data[column]

        cal_way = prm_config["prm"]["cal_way"]
        if cal_way == "count":
            values = self.cal_count(df_mark)
        elif (cal_way == "closedev_sum") and (cal_data_number == 1):
            values = self.cal_closedev_sum(data_dict2[0], df_mark, is_filter)
        elif (cal_way == "closedev_sum") and (cal_data_number == 2):
            df1 = data_dict2[1]/np.maximum(data_dict2[0], 1e-1)
            values = self.cal_closedev_sum(df1, df_mark, is_filter)
        elif cal_way == "var":
            values = self.cal_var(data_dict2[0], df_mark, is_filter)
        elif cal_way == "skew":
            values = self.cal_skew(data_dict2[0], df_mark, is_filter)
        elif cal_way == "ratio":
            values = self.cal_ratio(data_dict2[1], data_dict2[0], df_mark, is_filter)
        elif cal_way == "percount_ratio":
            values = self.cal_percount_ratio(data_dict2[1], data_dict2[0], df_mark, is_filter)
        elif cal_way == "strength":
            values = self.cal_strength(data_dict2[0], df_mark, is_filter)
        elif cal_way == "mediandev":
            values = self.cal_mediandev(data_dict2[0], df_mark, is_filter)

        return values
        
    def create_name(self, dict_data1, is_filter, cal_way, index_datalist, filter_way, filter_datalist):
        dict_data1["prm"]["cal_way"] = cal_way
        dict_data1["prm"]["cal_data_number"] = index_datalist["cal_data_number"]
        for i in range(0, index_datalist["cal_data_number"]):
            dict_data1["prm"]["cal_data" + str(i)] = index_datalist["cal_data" + str(i)]
        dict_data1["prm"]["filter_way"] = filter_way
        dict_data1["prm"]["filter_data_number"] = filter_datalist["filter_data_number"]
        for i in range(0, filter_datalist["filter_data_number"]):
            dict_data1["prm"]["filter_data" + str(i)] = filter_datalist["filter_data" + str(i)]

        name = "mk_lzy_"
        name = name + dict_data1["prm"]["filter_way"] + "_"
        for i in range(0, dict_data1["prm"]["filter_data_number"]):
            name = name + dict_data1["prm"]["filter_data"+str(i)] + "_"
        name = name + dict_data1["prm"]["filter_name"] +"_"
        name = name + dict_data1["prm"]["cal_way"] + "_"
        for i in range(0, dict_data1["prm"]["cal_data_number"]):
            name = name + dict_data1["prm"]["cal_data" + str(i)] + "_"
        name = name + "prm_10_0"
        dict_data1["feature_name"] = name

        return name, dict_data1

    def gengerate_name_and_calculate_factor(self):
        # 生成因子名称
        self.factor_name_list = []
        self.prm_config_list = {}
        filter_way_data = pd.Series({"no_filter":{"1":{"filter_data_number":0}},
        "ret":{"1":{"filter_data_number":2,"filter_data0":"close","filter_data1":"open"}},
        "ret_zeros":{"1":{"filter_data_number":2,"filter_data0":"close","filter_data1":"open"}},
        "closedev":{"1":{"filter_data_number":1,"filter_data0":"close"},
        "2":{"filter_data_number":1,"filter_data0":"open"},
        "3":{"filter_data_number":1,"filter_data0":"high"},
        "4":{"filter_data_number":1,"filter_data0":"low"},
        "5":{"filter_data_number":1,"filter_data0":"buy1"},
        "6":{"filter_data_number":1,"filter_data0":"sell1"}},
        "amplitude":{"1":{"filter_data_number":2,"filter_data0":"low","filter_data1":"high"}},
        "gap1":{"1":{"filter_data_number":2,"filter_data0":"open","filter_data1":"close"}},
        "gap2":{"1":{"filter_data_number":2,"filter_data0":"open","filter_data1":"close"}},
        "high_sub_buy":{"1":{"filter_data_number":3,"filter_data0":"open","filter_data1":"high","filter_data2":"bc1"}},
        "price_sub_buy":{"1":{"filter_data_number":3,"filter_data0":"open","filter_data1":"buy1","filter_data2":"bc1"},
        "2":{"filter_data_number":3,"filter_data0":"open","filter_data1":"sell1","filter_data2":"bc1"}},
        "low_sub_sell":{"1":{"filter_data_number":3,"filter_data0":"open","filter_data1":"low","filter_data2":"sell_cnt"}},
        "price_sub_sell":{"1":{"filter_data_number":3,"filter_data0":"open","filter_data1":"buy1","filter_data2":"sc1"},
        "2":{"filter_data_number":3,"filter_data0":"open","filter_data1":"sell1","filter_data2":"sc1"}},
        "price_spreads":{"1":{"filter_data_number":4,"filter_data0":"open","filter_data1":"high","filter_data2":"low","filter_data3":"close"}},
        "double_percount":{"1":{"filter_data_number":4,"filter_data0":"time_count","filter_data1":"bc1","filter_data2":"time_count","filter_data3":"sc1"},
        "2":{"filter_data_number":4,"filter_data0":"time_count","filter_data1":"amount","filter_data2":"time_count","filter_data3":"bc1"},
        "3":{"filter_data_number":4,"filter_data0":"time_count","filter_data1":"amount","filter_data2":"time_count","filter_data3":"sc1"}}, 
        "self1":{"1":{"filter_data_number":1,"filter_data0":"amount"},
        "2":{"filter_data_number":1,"filter_data0":"volume"},
        "3":{"filter_data_number":1,"filter_data0":"time_count"},
        "4":{"filter_data_number":1,"filter_data0":"bc1"},
        "5":{"filter_data_number":1,"filter_data0":"sc1"}},
        "self2":{"1":{"filter_data_number":2,"filter_data0":"bc1","filter_data1":"sc1"}},
        })

        filter_single1_namelist = ["timeseries_up","timeseries_down","timeseries_diff_rank_up","timeseries_diff_rank_down",
        "rolling_up_up","rolling_up_down","rolling_down_up","rolling_down_down"]#8种
        filter_single2_namelist = ["timeseries_ratio_up","timeseries_ratio_down","timeseries_diff_rank_up",
        "timeseries_diff_rank_down","rolling_up_up","rolling_up_down","rolling_down_up","rolling_down_down"]#8种
        filter_double1_namelist = ["timeseries_double_up","timeseries_double_down"]#2种
        filter_double2_namelist = ["timeseries_double_ratio_up","timeseries_double_ratio_down","timeseries_double_changes_up",
        "timeseries_double_changes_down","timeseries_double_rank_up","timeseries_double_rank_down"]#6种

        #no_filter 1种
        filter_all_name = pd.Series({"no_filter":[],
        "ret":filter_single1_namelist,#1x8
        "ret_zeros":filter_double1_namelist,#1x2
        "closedev":filter_single1_namelist,#6x8
        "amplitude":filter_single1_namelist,#1x8
        "gap1":filter_double1_namelist,#1x2
        "gap2":filter_single1_namelist,#1x8
        "high_sub_buy":filter_single1_namelist,#1x8
        "price_sub_buy":filter_single1_namelist,#2x8
        "low_sub_sell":filter_single1_namelist,#1x8
        "price_sub_sell":filter_single1_namelist,#2x8
        "price_spreads":filter_double1_namelist,#1x2
        "double_percount":filter_double1_namelist,#3x2
        "self1":filter_single2_namelist,#5x6
        "self2":filter_double2_namelist,#1x6
        })
        #总共168种筛选方式

        #30种指标，5040个因子
        index_way_data = {"count":{"1":{"cal_data_number":0}},
        "closedev_sum":{"1":{"cal_data_number":1,"cal_data0":"close"},
        "2":{"cal_data_number":1,"cal_data0":"open"},
        "3":{"cal_data_number":1,"cal_data0":"high"},
        "4":{"cal_data_number":1,"cal_data0":"low"},
        "5":{"cal_data_number":1,"cal_data0":"buy1"},
        "6":{"cal_data_number":1,"cal_data0":"sell1"}},
        "var":{"1":{"cal_data_number":1,"cal_data0":"amount"},
        "2":{"cal_data_number":1,"cal_data0":"vol"},
        "3":{"cal_data_number":1,"cal_data0":"time_count"},
        "4":{"cal_data_number":1,"cal_data0":"bc1"},
        "5":{"cal_data_number":1,"cal_data0":"sc1"}},
        "skew":{"1":{"cal_data_number":1,"cal_data0":"amount"},
        "2":{"cal_data_number":1,"cal_data0":"vol"},
        "3":{"cal_data_number":1,"cal_data0":"time_count"},
        "4":{"cal_data_number":1,"cal_data0":"bc1"},
        "5":{"cal_data_number":1,"cal_data0":"sc1"}},
        "ratio":{"1":{"cal_data_number":2,"cal_data0":"vol","cal_data1":"bc1"},
        "2":{"cal_data_number":2,"cal_data0":"vol","cal_data1":"sc1"}},
        "percount_ratio":{"1":{"cal_data_number":2,"cal_data0":"time_count","cal_data1":"bc1"},
        "2":{"cal_data_number":2,"cal_data0":"time_count","cal_data1":"sc1"}},
        "strength":{"1":{"cal_data_number":1,"cal_data0":"vol"},
        "2":{"cal_data_number":1,"cal_data0":"time_count"},
        "3":{"cal_data_number":1,"cal_data0":"amount"},
        "4":{"cal_data_number":1,"cal_data0":"bc1"},
        "5":{"cal_data_number":1,"cal_data0":"sc1"}},
        "mediandev":{"1":{"cal_data_number":1,"cal_data0":"vol"},
        "2":{"cal_data_number":1,"cal_data0":"amount"},
        "3":{"cal_data_number":1,"cal_data0":"time_count"},
        "4":{"cal_data_number":1,"cal_data0":"bc1"},
        "5":{"cal_data_number":1,"cal_data0":"sc1"}}}
        dict_data = {
            "prm": {
            }
        }
        # result_feature_names = sorted(list(pd.read_excel("E:/lzy/lzy提交第二批/feature_selection_summary(all).xlsx", sheet_name="pass", index_col=0).index))
        # result_feature_names = [name[:-4] for name in result_feature_names]
        for index in index_way_data.items():
            cal_way = index[0]
            index_data = index[1]
            for index_i in index_data.items():
                for filter in filter_way_data.items():
                    filter_way = filter[0]
                    filter_data = filter[1]
                    for filter_i in filter_data.items():
                        if filter[0] == "no_filter":
                            dict_data["prm"]["filter_name"] = "no_filter"
                            is_filter = False
                            index_datalist = index_i[1]
                            filter_datalist = filter_i[1]
                            if is_filter == False and cal_way == "count":
                                continue
                            name, dict_data = self.create_name(dict_data, is_filter, cal_way, index_datalist, filter_way, filter_datalist)
                            self.factor_name_list.append(name)
                            self.prm_config_list[name] = dict_data
                        else:
                            for filter_name in filter_all_name[filter[0]]:
                                dict_data["prm"]["filter_name"] = filter_name
                                is_filter = True
                                index_datalist = index_i[1]
                                filter_datalist = filter_i[1]
                                name, dict_data = self.create_name(dict_data, is_filter, cal_way, index_datalist, filter_way, filter_datalist)
                                self.factor_name_list.append(name)
                                self.prm_config_list[name] = dict_data

        for i in range(len(self.factor_name_list)):
            for row in tqdm(self.df.index, ncols=80):
                mask = ((self.df['endtime'] >= self.df.loc[row, 'backward_start_time']) & 
                    (self.df['endtime'] <= self.df.loc[row, 'backward_end_time']))
                temp = self.df.loc[mask, :]
                if not temp.empty:
                    try:
                        self.feature_df.loc[row, self.factor_name_list[i]] = self.filter_and_cal(temp, self.prm_config_list[self.factor_name_list[i]])
                    except Exception as e:
                        error_msg = f"Error calculating factor '{self.factor_name_list[i]}' at row {row}: {str(e)}"
                        print(error_msg)
                else:
                    self.feature_df.loc[row, self.factor_name_list[i]] = 0
        return None
            
        # # 计算因子（每计算一千个存一次）
        # temp_res = [self.process.remote(i) for i in tqdm(range(0,100))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_0_100.csv')
        # print('前100个因子计算完毕')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(100,1000))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_100_1000.csv')
        # print('前1000个因子计算完毕')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(1000,2000))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_1000_2000.csv')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(2000,3000))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_2000_3000.csv')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(3000,4000))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_3000_4000.csv')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(4000,5000))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_4000_5000.csv')

        # self.feature_df = pd.DataFrame()
        # self.feature_df['endtime'] = self.df['endtime']
        # temp_res = [self.process.remote(i) for i in tqdm(range(5000,len(self.factor_name_list)))]
        # tr_res = ray.get(temp_res)
        # ray.shutdown()
        # self.feature_df.to_csv('/home/<USER>/lzy_T0_alpha/automation_feature_5000_all.csv')
        # return None

class ml_modelling_for_combo():
    def feature_normalization(self, feature_df): # 去极值
        # 中位数拉回法去极值
        median_series = feature_df.median(axis=0)
        mad_series = feature_df.sub(median_series, axis=1).abs().median(axis=0)
        upper_bound = median_series + 15 * mad_series
        lower_bound = median_series - 15 * mad_series
        feature_df = feature_df.clip(lower=lower_bound, upper=upper_bound, axis=1)
        # Z-score标准化
        feature_df = feature_df.sub(feature_df.mean(axis=0), axis=1)
        feature_df = feature_df.div(feature_df.std(axis=0), axis=1)
        return feature_df.replace([np.nan], 0.0)
    
    def __init__(self, kwargs):
        '''
        注意时序标准化：
            （1）对于不同特征，做标准化（时序上）去量纲 √
            （2）对于价格，在时序节点上做标准化：一、全部除以某个时点的价格；二、查阅论文，寻找时序数据强制转为正态分布的方法
        '''
        self.backward_start_minute = kwargs['backward_start_minute']
        self.backward_end_minute = kwargs['backward_end_minute']
        _x_return_raw_df = pd.read_csv(f'/home/<USER>/lzy_T0_alpha/feature_return_{self.backward_start_minute}_{self.backward_end_minute}.csv')
        _x_transaction_raw_df = pd.read_csv(f'/home/<USER>/lzy_T0_alpha/feature_transaction_{self.backward_start_minute}_{self.backward_end_minute}.csv')
        _x_raw_df = pd.merge(_x_return_raw_df, _x_transaction_raw_df, on='endtime',how='left')
        self.x_features = _x_raw_df.columns.drop('endtime')
        self._x_label = f'x_backward_{self.backward_start_minute}_{self.backward_end_minute}'
        if kwargs['predict_type'] == 'five_class':
            _y_raw_df = pd.read_excel('/home/<USER>/lzy_T0_alpha/l_dataset.xlsx')
            _y_raw_df = _y_raw_df[['surge','rise','stable','dip','crash']]
            self._y_label = 'y_five_class'
        else:
            _y_raw_df = pd.read_csv(f'/home/<USER>/lzy_T0_alpha/y_label_{self.backward_start_minute}_{self.backward_start_minute}.csv')
            self._y_label = f'y_forward_{self.backward_start_minute}_{self.backward_start_minute}'
        self.join_df = pd.concat([_x_raw_df, _y_raw_df], axis=1)
        self.join_df['endtime'] = pd.to_datetime(self.join_df['endtime'])
        self.all_trade_days = sorted(list(set(self.join_df['endtime'].dt.normalize())))

        self.objective = kwargs['objective']
        self.eval_type = kwargs['eval_type']
        self.backward_rolling_days = kwargs['backward_rolling_days']
        self.forward_rolling_days = kwargs['forward_rolling_days']
        self.predict_type = kwargs['predict_type']
        '''
        默认第一个月不用：
            对于每一组超参数和t=T, T+5, T+10, T+15等时间点，使用从第t天到第t+4天（共5个交易日）的数据来训练一个模型，
        在随后5天区间评估这个模型，选择最大平均f1分数或最小交叉熵损失函数的超参数组合。
        滚动训练+训练样本时段：
            预测阶段，使用回溯窗口的n天数据，预测未来m天的结果。

        多分类概率预测方式：
            （1）使用XGBClassifier，使用交叉熵损失函数，输出当前时刻分类及其概率
            （2）使用XGBRegressor，不使用交叉熵损失函数，多输出回归+后处理
        '''
    def XGB_Classifier(self, train_df, test_df, prm):
        x_train = train_df[self.x_features].values.astype(np.float64)
        y_train_probs = train_df[['surge','rise','stable','dip','crash']].values.astype(np.float64)
        x_test = test_df[self.x_features].values.astype(np.float64)
        y_test_probs = test_df[['surge','rise','stable','dip','crash']].values.astype(np.float64)

        # 将概率标签转换为类别索引（取最大概率的类别）
        y_train_labels = np.argmax(y_train_probs, axis=1)
        y_test_labels = np.argmax(y_test_probs, axis=1)
        # 计算样本权重（根据概率置信度加权）
        sample_weights = np.max(y_train_probs, axis=1)

        model = xgb.XGBClassifier(
            objective = prm['objective'], # 'multi:softprob'
            num_class = 5,
            learning_rate = prm['learning_rate'],
            max_depth = int(prm['max_depth']),
            n_estimators = int(prm['n_estimators']),
            subsample = prm['subsample'],
            grow_policy='lossguide',
            max_leaves=int(prm['max_leaves']),
            eval_metric='mlogloss',
            early_stopping_rounds=10,
            reg_lambda=1.0, # L2正则化
            random_state = 42,
            n_jobs =15
        )
        model.fit(x_train, y_train_labels, sample_weight = sample_weights, # 概率高的样本权重更大
                    eval_set = [(x_test, y_test_labels)], verbose=True) #添加早停机制，防止过拟合
        
        # 计算交叉熵损失
        y_pred_probs = model.predict_proba(x_test)
        if len(y_test_labels) != len(y_pred_probs):
            log_loss_value = 10  # 形状不一致时设为固定值
        else:
            log_loss_value = log_loss(y_true=y_test_labels, y_pred=y_pred_probs, labels=[0, 1, 2, 3, 4])
        # 计算F1分数 在未来将被替换某一类别的F1分数
        y_pred = model.predict(x_test)
        macro_f1 = f1_score(y_true=y_test_labels, y_pred=y_pred, average='macro')
        # 特征重要性
        feature_importance = model.feature_importances_

        return log_loss_value, y_pred_probs, macro_f1, y_pred, feature_importance
    
    def softmax(x): # 归一化
        e_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return e_x / e_x.sum(axis=1, keepdims=True)

    def XGB_Regressor(self, train_df, test_df, prm):
        x_train = train_df[self.x_features].values.astype(np.float64)
        y_train_probs = train_df[['surge','rise','stable','dip','crash']].values.astype(np.float64)
        x_test = test_df[self.x_features].values.astype(np.float64)
        y_test_probs = test_df[['surge','rise','stable','dip','crash']].values.astype(np.float64)

        model = MultiOutputRegressor(
            xgb.XGBRegressor(
                objective=prm['objective'], # 'reg:squarederror'
                learning_rate = prm['learning_rate'],
                max_depth = int(prm['max_depth']),
                n_estimators = int(prm['n_estimators']),
                subsample = prm['subsample'],
                grow_policy='lossguide',
                max_leaves=int(prm['max_leaves']),
                reg_lambda=1.0, # L2正则化
                random_state=42,
                n_jobs = 15
            )
        ).fit(x_train, y_train_probs)

        y_pred_probs = self.softmax(model.predict(x_test))
        log_loss_value = log_loss(y_true=y_test_probs, y_pred=y_pred_probs, labels=[0, 1, 2, 3, 4])
        feature_importance = model.feature_importances_
        return log_loss_value, y_pred_probs, feature_importance
    
    def XGB_bayesian(self, prm, eval_type):
        initial_train_start_date = [0,5,10]
        initial_train_end_date = [5,10,15]
        initial_test_start_date = [5,10,15]
        initial_test_end_date = [10,15,20]

        initial_feature_importance_list = []
        f1_list = []
        log_loss_list = []
        for i in range(len(initial_train_start_date)):
            train_index_filter = ((self.join_df['endtime']>=self.all_trade_days[initial_train_start_date[i]]) & 
                                  (self.join_df['endtime']<=self.all_trade_days[initial_train_end_date[i]]))
            test_index_filter = ((self.join_df['endtime']>=self.all_trade_days[initial_test_start_date[i]]) &
                                 (self.join_df['endtime']<=self.all_trade_days[initial_test_end_date[i]]))
            train_df = self.join_df[train_index_filter].fillna(0)
            test_df = self.join_df[test_index_filter].fillna(0)
            train_df[self.x_features] = self.feature_normalization(train_df[self.x_features])
            test_df[self.x_features] = self.feature_normalization(test_df[self.x_features])
            if prm['objective'] == 'multi:softprob':
                log_loss_value, y_pred_probs, f1, y_pred, feature_importance = self.XGB_Classifier(train_df, test_df, prm)
                log_loss_list.append(log_loss_value)
                f1_list.append(f1)
                feature_importance = pd.DataFrame(feature_importance, index=[self.x_features], columns=[self.all_trade_days[initial_test_start_date[i]].normalize()])
                initial_feature_importance_list.append(feature_importance)
            elif prm['objective'] == 'reg:squarederror':
                log_loss_value, y_pred_probs, feature_importance = self.XGB_Regressor(train_df, test_df, prm)
                log_loss_list.append(log_loss_value)
                feature_importance = pd.DataFrame(feature_importance, index=[self.x_features], columns=[self.all_trade_days[initial_test_start_date[i]].normalize()])
                initial_feature_importance_list.append(feature_importance)
        if eval_type == 'log_loss': # BayesianOptimization 的设计哲学是最大化目标函数,这里想最小化目标函数，对目标函数取负
            return -np.nanmean(log_loss_list), pd.concat(initial_feature_importance_list, axis=1, ignore_index=False, sort=True)
        elif eval_type == 'f1':
            return np.nanmean(f1_list), pd.concat(initial_feature_importance_list, axis=1, ignore_index=False, sort=True)
    
    def find_best_prm(self):
        XGB_BO = BayesianOptimization(
            lambda max_depth, learning_rate, subsample, max_leaves, n_estimators: 
            self.XGB_bayesian(
                prm={
                    'objective': self.objective,  
                    'max_depth': int(max_depth),
                    'learning_rate': learning_rate,
                    'subsample': subsample,
                    'max_leaves': int(max_leaves),
                    'n_estimators': int(n_estimators)
                },
                eval_type=self.eval_type
            )[0],
            pbounds={
                "max_depth": (3, 10),               # 树深度范围
                "learning_rate": (0.01, 0.1),       # 学习率范围
                "subsample": (0.7, 1.0),            # 样本采样比例
                "max_leaves": (8, 64),              # 最大叶子节点数量
                "n_estimators": (80, 200)           # 树的数量
            },
            random_state=666
        )
        if self.eval_type == 'log_loss':
            XGB_BO.maximize(
                init_points=5,  # 初始随机探索次数
                n_iter=25,       # 贝叶斯优化迭代次数
            )
            self.best_prm = XGB_BO.max['params']
        elif self.eval_type == 'f1':
            XGB_BO.maximize(
                init_points=5,  
                n_iter=25,         
            )
            self.best_prm = XGB_BO.max['params']
        return None
    
    def rolling_train(self):
        self.best_prm['objective'] = self.objective
        if kwargs['is_rolling']:
            update_index_list = sorted(list(set(list(np.arange(19, len(self.all_trade_days), self.forward_rolling_days)) + 
                                                [len(self.all_trade_days) - 1]))) #生成更新点列表
            pred_y_df_list = []
            pred_y_prob_df_list = []
            feature_importance_list = []
            f1_list = pd.Series()
            log_loss_list = pd.Series()
            for update_index_ in range(len(update_index_list)):
                if update_index_ == len(update_index_list) - 1:
                    continue
                # 左开右闭
                test_start_date = self.all_trade_days[update_index_list[update_index_] + 1]
                if update_index_ == len(update_index_list) - 2:
                    test_end_date = self.all_trade_days[update_index_list[update_index_ + 1]]
                else:
                    test_end_date = self.all_trade_days[update_index_list[update_index_ + 1] + 1]
                if update_index_list[update_index_] - self.backward_rolling_days + 1 < 0:
                    train_start_date = self.all_trade_days[0]
                else:
                    train_start_date = self.all_trade_days[update_index_list[update_index_] - self.backward_rolling_days + 1]
                train_end_date = self.all_trade_days[update_index_list[update_index_] + 1]
                train_index_filter = ((self.join_df['endtime']>=train_start_date) & (self.join_df['endtime']<=train_end_date))
                test_index_filter = ((self.join_df['endtime']>=test_start_date) & (self.join_df['endtime']<=test_end_date))
                
                train_df = self.join_df[train_index_filter].fillna(0)
                test_df = self.join_df[test_index_filter].fillna(0)
                train_df[self.x_features] = self.feature_normalization(train_df[self.x_features])
                test_df[self.x_features] = self.feature_normalization(test_df[self.x_features])

                if kwargs['model_name'] == 'xgb': # 未考虑时序依赖
                    if self.objective == 'multi:softprob':
                        log_loss_value, pred_y_prob, f1, pred_y, feature_importance = self.XGB_Classifier(train_df, test_df, self.best_prm)
                        log_loss_list.loc[test_start_date] = log_loss_value
                        f1_list.loc[test_start_date] = f1
                        feature_importance = pd.DataFrame(feature_importance, index=[self.x_features], columns=[test_start_date])
                        feature_importance_list.append(feature_importance)
                        pred_y_prob = pd.DataFrame(pred_y_prob, index=test_df['endtime'], columns=['surge','rise','stable','dip','crash'])
                        pred_y_prob_df_list.append(pred_y_prob)
                        pred_y = pd.DataFrame(pred_y, index=test_df['endtime'], columns=[self.predict_type])
                        pred_y_df_list.append(pred_y)
                        self._model_label = f'XGB_Classifier_rolling_{self.backward_rolling_days}_{self.forward_rolling_days}'

                    elif self.objective == 'reg:squarederror':
                        log_loss_value, pred_y_prob, feature_importance = self.XGB_Regressor(train_df, test_df, self.best_prm)
                        log_loss_list.loc[test_start_date] = log_loss_value
                        feature_importance = pd.DataFrame(feature_importance, index=[self.x_features], columns=[test_start_date])
                        feature_importance_list.append(feature_importance)
                        pred_y_prob = pd.DataFrame(pred_y_prob, index=test_df['endtime'], columns=['surge','rise','stable','dip','crash'])
                        pred_y_prob_df_list.append(pred_y_prob)
                        self._modsel_label = f'XGB_Regressor_rolling_{self.backward_rolling_days}_{self.forward_rolling_days}'

            if not os.path.exists(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}'):
                    os.makedirs(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}')
            feature_importance_all = pd.concat(feature_importance_list, axis=1, ignore_index=False, sort=True)
            if self.objective == 'multi:softprob':
                pred_y_df = pd.concat(pred_y_df_list, axis=0, ignore_index=False, sort=True)
                pred_y_prob_df = pd.concat(pred_y_prob_df_list, axis=0, ignore_index=False, sort=True)
                log_loss_list.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/log_loss.csv')
                pred_y_prob_df.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/pred_y_prob_df.csv')
                f1_list.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/f1.csv')
                pred_y_df.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/pred_y_df.csv')
                feature_importance_all.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/feature_importance_all.csv')
            elif self.objective == 'reg:squarederror':
                pred_y_prob_df = pd.concat(pred_y_prob_df_list, axis=0, ignore_index=False, sort=True)
                log_loss_list.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/log_loss.csv')
                pred_y_prob_df.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/pred_y_prob_df.csv')
                feature_importance_all.to_csv(f'/home/<USER>/lzy_T0_alpha/{self._x_label}_{self._y_label}_{self._model_label}/feature_importance_all.csv')
        
if __name__ == '__main__':
    data = pd.read_excel('/home/<USER>/lzy_T0_alpha/l_dataset.xlsx')
    kwargs = {#生成因子回溯时间和标签预测周期
              'backward_start_minute': 10, 
              'backward_end_minute': 0, 
              'forward_start_minute': 5, 
              'forward_end_minute': 10,
              #标签预测类型
              'predict_type':'five_class',
              #模型是否滚动训练及滚动参数
              'is_rolling':True,
              'backward_rolling_days':50, # 尝试天数为(5,2),(7,2),(10,2),(10,5),(15,5),(20,5),(20,10),(50,10),(50,20)
              'forward_rolling_days':20, 
              #模型类型
              'model_name':'xgb',
              #评价标准
              'objective':'reg:squarederror',
              'eval_type':'log_loss'
              }
    # 实例化因子计算
    # feature = feature_(data, kwargs)
    # 计算时间戳
    # start = time.time()
    # feature._time_interval()
    # end = time.time()
    # print(f"时间戳计算耗时：{end - start}")
    # 计算因子
    # start = time.time()
    # feature.factor_transaction_generation()
    # feature.factor_return_generation()
    # feature.factor_imbalance_generation()
    # end = time.time()
    # print(f"因子计算耗时：{end - start}")
    # feature.feature_df.to_csv('feature.csv')
    
    
    # 实例化自动生成特征
    automation_feature = automation_feature_(data, kwargs)
    # 计算时间戳
    start = time.time()
    automation_feature._time_interval()
    end = time.time()
    print(f"时间戳计算耗时：{end - start}")
    # 计算因子
    start = time.time()
    automation_feature.gengerate_name_and_calculate_factor()
    end = time.time()
    print(f"因子计算耗时：{end - start}")
    automation_feature.feature_df.to_csv('automation_feature.csv')

    # 实例化模型训练
    # model_for_combo = ml_modelling_for_combo(kwargs)
    # 贝叶斯调参
    # start = time.time()
    # model_for_combo.find_best_prm()
    # end = time.time()
    # print(f'贝叶斯调参耗时{end-start}')
    # 滚动训练
    # model_for_combo.rolling_train()
